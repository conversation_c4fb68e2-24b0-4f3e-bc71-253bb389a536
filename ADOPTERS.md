# Adopters of MPI Operator

This page contains a list of organizations who are using MPI Operator. If you'd like to be included here, please send a pull request which modifies this file. Please keep the list in alphabetical order.

| Organization | Contact |
| ------------ | ------- |
| [Alibaba Cloud](https://us.alibabacloud.com/) | [<PERSON>](https://github.com/cheyang) |
| [Alibaba DAMO Academy](https://damo.alibaba.com/) | [<PERSON><PERSON>](https://damo.alibaba.com/about/) |
| [Amazon Web Services](https://aws.amazon.com/) | [<PERSON><PERSON><PERSON>](https://github.com/Jeffwan) |
| [Ant Group](https://www.antgroup.com/) | [<PERSON>](https://github.com/terrytangyuan) and [<PERSON>](https://github.com/ywskycn/) |
| [Atrio](https://www.atrio.io/) | [<PERSON>](https://github.com/cesargomez) |
| [Bloomberg](https://www.bloomberg.com/) | [<PERSON><PERSON><PERSON>](https://github.com/czheng94) and [<PERSON>](https://github.com/yuzisun) |
| [Bytedance](https://www.bytedance.com/) | [Ce Gao](https://github.com/gaocegege) |
| [Huawei](https://www.huawei.com/) | [Fei Xu](https://github.com/fisherxu) |
| [Iguazio](https://www.iguazio.com/) | [Yaron Haviv](https://github.com/yaronha) |
| [Mellanox Technologies](https://www.mellanox.com/) | [Vitaliy Razinkov](https://github.com/vtlrazin) |
| [NVIDIA](https://www.nvidia.com/) | [Rong Ou](https://github.com/rongou) |
| [Pinduoduo](https://en.pinduoduo.com/) | [Shuwen Wang](https://github.com/antshuwen) |
| [PITS Global Data Recovery Services](https://www.pitsdatarecovery.net/) | [Benjamin Trudeau](https://github.com/benjx1990) |
| [Polyaxon](https://polyaxon.com/) | [Mourad Mourafiq](https://github.com/mouradmourafiq) |
| [Qutoutiao](https://www.qutoutiao.net/) | [Zhaojing Yu](https://github.com/yuzhaojing) |
| [Run:AI](https://www.run.ai/) | [Rotem Elad](https://github.com/roteme-runai) |
| [Tencent](http://tencent.com/en-us/) | [Lei Xue](https://github.com/carmark) |
