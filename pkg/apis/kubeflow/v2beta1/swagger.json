{"swagger": "2.0", "info": {"description": "Python SDK for MPI-Operator", "title": "m<PERSON><PERSON>b", "version": "v2beta1"}, "paths": {}, "definitions": {"k8s.io.apimachinery.pkg.runtime.RawExtension": {"description": "RawExtension is used to hold extensions in external versions.\n\nTo use this, make a field which has RawExtension as its type in your external, versioned struct, and Object in your internal struct. You also need to register your various plugin types.\n\n// Internal package:\n\n\ttype MyAPIObject struct {\n\t\truntime.TypeMeta `json:\",inline\"`\n\t\tMyPlugin runtime.Object `json:\"myPlugin\"`\n\t}\n\n\ttype PluginA struct {\n\t\tAOption string `json:\"aOption\"`\n\t}\n\n// External package:\n\n\ttype MyAPIObject struct {\n\t\truntime.TypeMeta `json:\",inline\"`\n\t\tMyPlugin runtime.RawExtension `json:\"myPlugin\"`\n\t}\n\n\ttype PluginA struct {\n\t\tAOption string `json:\"aOption\"`\n\t}\n\n// On the wire, the JSON will look something like this:\n\n\t{\n\t\t\"kind\":\"MyAPIObject\",\n\t\t\"apiVersion\":\"v1\",\n\t\t\"myPlugin\": {\n\t\t\t\"kind\":\"PluginA\",\n\t\t\t\"aOption\":\"foo\",\n\t\t},\n\t}\n\nSo what happens? Decode first uses json or yaml to unmarshal the serialized data into your external MyAPIObject. That causes the raw JSON to be stored, but not unpacked. The next step is to copy (using pkg/conversion) into the internal struct. The runtime package's DefaultScheme has conversion functions installed which will unpack the JSON stored in RawExtension, turning it into the correct object type, and storing it in the Object. (TODO: In the case where the object is of an unknown type, a runtime.Unknown object will be created and stored.)", "type": "object"}, "k8s.io.apimachinery.pkg.runtime.TypeMeta": {"description": "TypeMeta is shared by all top level objects. The proper way to use it is to inline it in your type, like this:\n\n\ttype MyAwesomeAPIObject struct {\n\t     runtime.TypeMeta    `json:\",inline\"`\n\t     ... // other fields\n\t}\n\nfunc (obj *MyAwesomeAPIObject) SetGroupVersionKind(gvk *metav1.GroupVersionKind) { metav1.UpdateTypeMeta(obj,gvk) }; GroupVersionKind() *GroupVersionKind\n\nTypeMeta is provided here for convenience. You may use it directly from this package or define your own with the same fields.", "type": "object", "properties": {"apiVersion": {"type": "string"}, "kind": {"type": "string"}}}, "k8s.io.apimachinery.pkg.runtime.Unknown": {"description": "Unknown allows api objects with unknown types to be passed-through. This can be used to deal with the API objects from a plug-in. Unknown objects still have functioning TypeMeta features-- kind, version, etc. metadata and field mutatation.", "type": "object", "required": ["ContentEncoding", "ContentType"], "properties": {"ContentEncoding": {"description": "ContentEncoding is encoding used to encode 'Raw' data. Unspecified means no encoding.", "type": "string", "default": ""}, "ContentType": {"description": "ContentType  is serialization method used to serialize 'Raw'. Unspecified means ContentTypeJSON.", "type": "string", "default": ""}, "apiVersion": {"type": "string"}, "kind": {"type": "string"}}}, "k8s.io.apimachinery.pkg.version.Info": {"description": "Info contains versioning information. how we'll want to distribute that information.", "type": "object", "required": ["major", "minor", "gitVersion", "gitCommit", "gitTreeState", "buildDate", "goVersion", "compiler", "platform"], "properties": {"buildDate": {"type": "string", "default": ""}, "compiler": {"type": "string", "default": ""}, "gitCommit": {"type": "string", "default": ""}, "gitTreeState": {"type": "string", "default": ""}, "gitVersion": {"type": "string", "default": ""}, "goVersion": {"type": "string", "default": ""}, "major": {"type": "string", "default": ""}, "minor": {"type": "string", "default": ""}, "platform": {"type": "string", "default": ""}}}, "v1.APIGroup": {"description": "APIGroup contains the name, the supported versions, and the preferred version of a group.", "type": "object", "required": ["name", "versions"], "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "name": {"description": "name is the name of the group.", "type": "string", "default": ""}, "preferredVersion": {"description": "preferredVersion is the version preferred by the API server, which probably is the storage version.", "default": {}, "$ref": "#/definitions/v1.GroupVersionForDiscovery"}, "serverAddressByClientCIDRs": {"description": "a map of client CIDR to server address that is serving this group. This is to help clients reach servers in the most network-efficient way possible. Clients can use the appropriate server address as per the CIDR that they match. In case of multiple matches, clients should use the longest matching CIDR. The server returns only those CIDRs that it thinks that the client can match. For example: the master will return an internal IP CIDR only, if the client reaches the server using an internal IP. Server looks at X-Forwarded-For header or X-Real-Ip header or request.RemoteAddr (in that order) to get the client IP.", "type": "array", "items": {"default": {}, "$ref": "#/definitions/v1.ServerAddressByClientCIDR"}, "x-kubernetes-list-type": "atomic"}, "versions": {"description": "versions are the versions supported in this group.", "type": "array", "items": {"default": {}, "$ref": "#/definitions/v1.GroupVersionForDiscovery"}, "x-kubernetes-list-type": "atomic"}}}, "v1.APIGroupList": {"description": "APIGroupList is a list of APIGroup, to allow clients to discover the API at /apis.", "type": "object", "required": ["groups"], "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "groups": {"description": "groups is a list of APIGroup.", "type": "array", "items": {"default": {}, "$ref": "#/definitions/v1.APIGroup"}, "x-kubernetes-list-type": "atomic"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}}}, "v1.APIResource": {"description": "APIResource specifies the name of a resource and whether it is namespaced.", "type": "object", "required": ["name", "singularName", "namespaced", "kind", "verbs"], "properties": {"categories": {"description": "categories is a list of the grouped resources this resource belongs to (e.g. 'all')", "type": "array", "items": {"type": "string", "default": ""}, "x-kubernetes-list-type": "atomic"}, "group": {"description": "group is the preferred group of the resource.  Empty implies the group of the containing resource list. For subresources, this may have a different value, for example: Scale\".", "type": "string"}, "kind": {"description": "kind is the kind for the resource (e.g. 'Foo' is the kind for a resource 'foo')", "type": "string", "default": ""}, "name": {"description": "name is the plural name of the resource.", "type": "string", "default": ""}, "namespaced": {"description": "namespaced indicates if a resource is namespaced or not.", "type": "boolean", "default": false}, "shortNames": {"description": "shortNames is a list of suggested short names of the resource.", "type": "array", "items": {"type": "string", "default": ""}, "x-kubernetes-list-type": "atomic"}, "singularName": {"description": "singularName is the singular name of the resource.  This allows clients to handle plural and singular opaquely. The singularName is more correct for reporting status on a single item and both singular and plural are allowed from the kubectl CLI interface.", "type": "string", "default": ""}, "storageVersionHash": {"description": "The hash value of the storage version, the version this resource is converted to when written to the data store. Value must be treated as opaque by clients. Only equality comparison on the value is valid. This is an alpha feature and may change or be removed in the future. The field is populated by the apiserver only if the StorageVersionHash feature gate is enabled. This field will remain optional even if it graduates.", "type": "string"}, "verbs": {"description": "verbs is a list of supported kube verbs (this includes get, list, watch, create, update, patch, delete, deletecollection, and proxy)", "type": "array", "items": {"type": "string", "default": ""}}, "version": {"description": "version is the preferred version of the resource.  Empty implies the version of the containing resource list For subresources, this may have a different value, for example: v1 (while inside a v1beta1 version of the core resource's group)\".", "type": "string"}}}, "v1.APIResourceList": {"description": "APIResourceList is a list of APIResource, it is used to expose the name of the resources supported in a specific group and version, and if the resource is namespaced.", "type": "object", "required": ["groupVersion", "resources"], "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "groupVersion": {"description": "groupVersion is the group and version this APIResourceList is for.", "type": "string", "default": ""}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "resources": {"description": "resources contains the name of the resources and if they are namespaced.", "type": "array", "items": {"default": {}, "$ref": "#/definitions/v1.APIResource"}, "x-kubernetes-list-type": "atomic"}}}, "v1.APIVersions": {"description": "APIVersions lists the versions that are available, to allow clients to discover the API at /api, which is the root path of the legacy v1 API.", "type": "object", "required": ["versions", "serverAddressByClientCIDRs"], "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "serverAddressByClientCIDRs": {"description": "a map of client CIDR to server address that is serving this group. This is to help clients reach servers in the most network-efficient way possible. Clients can use the appropriate server address as per the CIDR that they match. In case of multiple matches, clients should use the longest matching CIDR. The server returns only those CIDRs that it thinks that the client can match. For example: the master will return an internal IP CIDR only, if the client reaches the server using an internal IP. Server looks at X-Forwarded-For header or X-Real-Ip header or request.RemoteAddr (in that order) to get the client IP.", "type": "array", "items": {"default": {}, "$ref": "#/definitions/v1.ServerAddressByClientCIDR"}, "x-kubernetes-list-type": "atomic"}, "versions": {"description": "versions are the api versions that are available.", "type": "array", "items": {"type": "string", "default": ""}, "x-kubernetes-list-type": "atomic"}}}, "v1.ApplyOptions": {"description": "ApplyOptions may be provided when applying an API object. FieldManager is required for apply requests. ApplyOptions is equivalent to PatchOptions. It is provided as a convenience with documentation that speaks specifically to how the options fields relate to apply.", "type": "object", "required": ["force", "fieldManager"], "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "dryRun": {"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "type": "array", "items": {"type": "string", "default": ""}, "x-kubernetes-list-type": "atomic"}, "fieldManager": {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint. This field is required.", "type": "string", "default": ""}, "force": {"description": "Force is going to \"force\" Apply requests. It means user will re-acquire conflicting fields owned by other people.", "type": "boolean", "default": false}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}}}, "v1.Condition": {"description": "Condition contains details for one aspect of the current state of this API Resource.", "type": "object", "required": ["type", "status", "lastTransitionTime", "reason", "message"], "properties": {"lastTransitionTime": {"description": "lastTransitionTime is the last time the condition transitioned from one status to another. This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.", "$ref": "#/definitions/v1.Time"}, "message": {"description": "message is a human readable message indicating details about the transition. This may be an empty string.", "type": "string", "default": ""}, "observedGeneration": {"description": "observedGeneration represents the .metadata.generation that the condition was set based upon. For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date with respect to the current state of the instance.", "type": "integer", "format": "int64"}, "reason": {"description": "reason contains a programmatic identifier indicating the reason for the condition's last transition. Producers of specific condition types may define expected values and meanings for this field, and whether the values are considered a guaranteed API. The value should be a CamelCase string. This field may not be empty.", "type": "string", "default": ""}, "status": {"description": "status of the condition, one of True, False, Unknown.", "type": "string", "default": ""}, "type": {"description": "type of condition in CamelCase or in foo.example.com/CamelCase.", "type": "string", "default": ""}}}, "v1.CreateOptions": {"description": "CreateOptions may be provided when creating an API object.", "type": "object", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "dryRun": {"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "type": "array", "items": {"type": "string", "default": ""}, "x-kubernetes-list-type": "atomic"}, "fieldManager": {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.", "type": "string"}, "fieldValidation": {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}}}, "v1.DeleteOptions": {"description": "DeleteOptions may be provided when deleting an API object.", "type": "object", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "dryRun": {"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "type": "array", "items": {"type": "string", "default": ""}, "x-kubernetes-list-type": "atomic"}, "gracePeriodSeconds": {"description": "The duration in seconds before the object should be deleted. Value must be non-negative integer. The value zero indicates delete immediately. If this value is nil, the default grace period for the specified type will be used. Defaults to a per object value if not specified. zero means delete immediately.", "type": "integer", "format": "int64"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "orphanDependents": {"description": "Deprecated: please use the PropagationPolicy, this field will be deprecated in 1.7. Should the dependent objects be orphaned. If true/false, the \"orphan\" finalizer will be added to/removed from the object's finalizers list. Either this field or PropagationPolicy may be set, but not both.", "type": "boolean"}, "preconditions": {"description": "Must be fulfilled before a deletion is carried out. If not possible, a 409 Conflict status will be returned.", "$ref": "#/definitions/v1.Preconditions"}, "propagationPolicy": {"description": "Whether and how garbage collection will be performed. Either this field or OrphanDependents may be set, but not both. The default policy is decided by the existing finalizer set in the metadata.finalizers and the resource-specific default policy. Acceptable values are: 'Orphan' - orphan the dependents; 'Background' - allow the garbage collector to delete the dependents in the background; 'Foreground' - a cascading policy that deletes all dependents in the foreground.", "type": "string"}}}, "v1.Duration": {"description": "Duration is a wrapper around time.Duration which supports correct marshaling to YAML and JSON. In particular, it marshals into strings, which can be used as map keys in json.", "type": "string"}, "v1.FieldSelectorRequirement": {"description": "FieldSelectorRequirement is a selector that contains values, a key, and an operator that relates the key and values.", "type": "object", "required": ["key", "operator"], "properties": {"key": {"description": "key is the field selector key that the requirement applies to.", "type": "string", "default": ""}, "operator": {"description": "operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists, DoesNotExist. The list of operators may grow in the future.", "type": "string", "default": ""}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty.", "type": "array", "items": {"type": "string", "default": ""}, "x-kubernetes-list-type": "atomic"}}}, "v1.FieldsV1": {"description": "FieldsV1 stores a set of fields in a data structure like a Trie, in JSON format.\n\nEach key is either a '.' representing the field itself, and will always map to an empty set, or a string representing a sub-field or item. The string will follow one of these four formats: 'f:<name>', where <name> is the name of a field in a struct, or key in a map 'v:<value>', where <value> is the exact json formatted value of a list item 'i:<index>', where <index> is position of a item in a list 'k:<keys>', where <keys> is a map of  a list item's key fields to their unique values If a key maps to an empty Fields value, the field that key represents is part of the set.\n\nThe exact format is defined in sigs.k8s.io/structured-merge-diff", "type": "object"}, "v1.GetOptions": {"description": "GetOptions is the standard query options to the standard REST get call.", "type": "object", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "resourceVersion": {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "type": "string"}}}, "v1.GroupKind": {"description": "GroupKind specifies a Group and a Kind, but does not force a version.  This is useful for identifying concepts during lookup stages without having partially valid types", "type": "object", "required": ["group", "kind"], "properties": {"group": {"type": "string", "default": ""}, "kind": {"type": "string", "default": ""}}}, "v1.GroupResource": {"description": "GroupResource specifies a Group and a Resource, but does not force a version.  This is useful for identifying concepts during lookup stages without having partially valid types", "type": "object", "required": ["group", "resource"], "properties": {"group": {"type": "string", "default": ""}, "resource": {"type": "string", "default": ""}}}, "v1.GroupVersion": {"description": "GroupVersion contains the \"group\" and the \"version\", which uniquely identifies the API.", "type": "object", "required": ["group", "version"], "properties": {"group": {"type": "string", "default": ""}, "version": {"type": "string", "default": ""}}}, "v1.GroupVersionForDiscovery": {"description": "GroupVersion contains the \"group/version\" and \"version\" string of a version. It is made a struct to keep extensibility.", "type": "object", "required": ["groupVersion", "version"], "properties": {"groupVersion": {"description": "groupVersion specifies the API group and version in the form \"group/version\"", "type": "string", "default": ""}, "version": {"description": "version specifies the version in the form of \"version\". This is to save the clients the trouble of splitting the GroupVersion.", "type": "string", "default": ""}}}, "v1.GroupVersionKind": {"description": "GroupVersionKind unambiguously identifies a kind.  It doesn't anonymously include GroupVersion to avoid automatic coercion.  It doesn't use a GroupVersion to avoid custom marshalling", "type": "object", "required": ["group", "version", "kind"], "properties": {"group": {"type": "string", "default": ""}, "kind": {"type": "string", "default": ""}, "version": {"type": "string", "default": ""}}}, "v1.GroupVersionResource": {"description": "GroupVersionResource unambiguously identifies a resource.  It doesn't anonymously include GroupVersion to avoid automatic coercion.  It doesn't use a GroupVersion to avoid custom marshalling", "type": "object", "required": ["group", "version", "resource"], "properties": {"group": {"type": "string", "default": ""}, "resource": {"type": "string", "default": ""}, "version": {"type": "string", "default": ""}}}, "v1.InternalEvent": {"description": "InternalEvent makes watch.Event versioned", "type": "object", "required": ["Type", "Object"], "properties": {"Object": {"description": "Object is:\n * If Type is Added or Modified: the new state of the object.\n * If Type is Deleted: the state of the object immediately before deletion.\n * If Type is Bookmark: the object (instance of a type being watched) where\n   only ResourceVersion field is set. On successful restart of watch from a\n   bookmark resourceVersion, client is guaranteed to not get repeat event\n   nor miss any events.\n * If Type is Error: *api.Status is recommended; other types may make sense\n   depending on context.", "$ref": "#/definitions/k8s.io.apimachinery.pkg.runtime.Object"}, "Type": {"type": "string", "default": ""}}}, "v1.LabelSelector": {"description": "A label selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty label selector matches all objects. A null label selector matches no objects.", "type": "object", "properties": {"matchExpressions": {"description": "matchExpressions is a list of label selector requirements. The requirements are ANDed.", "type": "array", "items": {"default": {}, "$ref": "#/definitions/v1.LabelSelectorRequirement"}, "x-kubernetes-list-type": "atomic"}, "matchLabels": {"description": "matchLabels is a map of {key,value} pairs. A single {key,value} in the match<PERSON>abe<PERSON> map is equivalent to an element of matchExpressions, whose key field is \"key\", the operator is \"In\", and the values array contains only \"value\". The requirements are ANDed.", "type": "object", "additionalProperties": {"type": "string", "default": ""}}}, "x-kubernetes-map-type": "atomic"}, "v1.LabelSelectorRequirement": {"description": "A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "type": "object", "required": ["key", "operator"], "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string", "default": ""}, "operator": {"description": "operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.", "type": "string", "default": ""}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.", "type": "array", "items": {"type": "string", "default": ""}, "x-kubernetes-list-type": "atomic"}}}, "v1.List": {"description": "List holds a list of objects, which may not be known by the server.", "type": "object", "required": ["items"], "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "items": {"description": "List of objects", "type": "array", "items": {"$ref": "#/definitions/k8s.io.apimachinery.pkg.runtime.RawExtension"}}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"description": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "default": {}, "$ref": "#/definitions/v1.ListMeta"}}}, "v1.ListMeta": {"description": "ListMeta describes metadata that synthetic resources must have, including lists and various status objects. A resource may have only one of {ObjectMeta, ListMeta}.", "type": "object", "properties": {"continue": {"description": "continue may be set if the user set a limit on the number of items returned, and indicates that the server has more data available. The value is opaque and may be used to issue another request to the endpoint that served this list to retrieve the next set of available objects. Continuing a consistent list may not be possible if the server configuration has changed or more than a few minutes have passed. The resourceVersion field returned when using this continue value will be identical to the value in the first response, unless you have received this token from an error message.", "type": "string"}, "remainingItemCount": {"description": "remainingItemCount is the number of subsequent items in the list which are not included in this list response. If the list request contained label or field selectors, then the number of remaining items is unknown and the field will be left unset and omitted during serialization. If the list is complete (either because it is not chunking or because this is the last chunk), then there are no more remaining items and this field will be left unset and omitted during serialization. Servers older than v1.15 do not set this field. The intended use of the remainingItemCount is *estimating* the size of a collection. Clients should not rely on the remainingItemCount to be set or to be exact.", "type": "integer", "format": "int64"}, "resourceVersion": {"description": "String that identifies the server's internal version of this object that can be used by clients to determine when objects have changed. Value must be treated as opaque by clients and passed unmodified back to the server. Populated by the system. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency", "type": "string"}, "selfLink": {"description": "Deprecated: selfLink is a legacy read-only field that is no longer populated by the system.", "type": "string"}}}, "v1.ListOptions": {"description": "ListOptions is the query options to a standard REST list call.", "type": "object", "properties": {"allowWatchBookmarks": {"description": "allowWatchBookmarks requests watch events with type \"BOOKMARK\". Servers that do not implement bookmarks may ignore this flag and bookmarks are sent at the server's discretion. Clients should not assume bookmarks are returned at any specific interval, nor may they assume the server will send any BOOKMARK event during a session. If this is not a watch, this field is ignored.", "type": "boolean"}, "apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "continue": {"description": "The continue option should be set when retrieving more results from the server. Since this value is server defined, clients may only use the continue value from a previous query result with identical query parameters (except for the value of continue) and the server may reject a continue value it does not recognize. If the specified continue value is no longer valid whether due to expiration (generally five to fifteen minutes) or a configuration change on the server, the server will respond with a 410 ResourceExpired error together with a continue token. If the client needs a consistent list, it must restart their list without the continue field. Otherwise, the client may send another list request with the token received with the 410 error, the server will respond with a list starting from the next key, but from the latest snapshot, which is inconsistent from the previous list results - objects that are created, modified, or deleted after the first list request will be included in the response, as long as their keys are after the \"next key\".\n\nThis field is not supported when watch is true. Clients may start a watch from the last resourceVersion value returned by the server and not miss any modifications.", "type": "string"}, "fieldSelector": {"description": "A selector to restrict the list of returned objects by their fields. Defaults to everything.", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "labelSelector": {"description": "A selector to restrict the list of returned objects by their labels. Defaults to everything.", "type": "string"}, "limit": {"description": "limit is a maximum number of responses to return for a list call. If more items exist, the server will set the `continue` field on the list metadata to a value that can be used with the same initial query to retrieve the next set of results. Setting a limit may return fewer than the requested amount of items (up to zero items) in the event all requested objects are filtered out and clients should only use the presence of the continue field to determine whether more results are available. Servers may choose not to support the limit argument and will return all of the available results. If limit is specified and the continue field is empty, clients may assume that no more results are available. This field is not supported if watch is true.\n\nThe server guarantees that the objects returned when using continue will be identical to issuing a single list call without a limit - that is, no objects created, modified, or deleted after the first request is issued will be included in any subsequent continued requests. This is sometimes referred to as a consistent snapshot, and ensures that a client that is using limit to receive smaller chunks of a very large result can ensure they see all possible objects. If objects are updated during a chunked list the version of the object that was present at the time the first list result was calculated is returned.", "type": "integer", "format": "int64"}, "resourceVersion": {"description": "resourceVersion sets a constraint on what resource versions a request may be served from. See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "type": "string"}, "resourceVersionMatch": {"description": "resourceVersionMatch determines how resourceVersion is applied to list calls. It is highly recommended that resourceVersionMatch be set for list calls where resourceVersion is set See https://kubernetes.io/docs/reference/using-api/api-concepts/#resource-versions for details.\n\nDefaults to unset", "type": "string"}, "sendInitialEvents": {"description": "`sendInitialEvents=true` may be set together with `watch=true`. In that case, the watch stream will begin with synthetic events to produce the current state of objects in the collection. Once all such events have been sent, a synthetic \"Bookmark\" event  will be sent. The bookmark will report the ResourceVersion (RV) corresponding to the set of objects, and be marked with `\"k8s.io/initial-events-end\": \"true\"` annotation. Afterwards, the watch stream will proceed as usual, sending watch events corresponding to changes (subsequent to the RV) to objects watched.\n\nWhen `sendInitialEvents` option is set, we require `resourceVersionMatch` option to also be set. The semantic of the watch request is as following: - `resourceVersionMatch` = NotOlderThan\n  is interpreted as \"data at least as new as the provided `resourceVersion`\"\n  and the bookmark event is send when the state is synced\n  to a `resourceVersion` at least as fresh as the one provided by the ListOptions.\n  If `resourceVersion` is unset, this is interpreted as \"consistent read\" and the\n  bookmark event is send when the state is synced at least to the moment\n  when request started being processed.\n- `resourceVersionMatch` set to any other value or unset\n  Invalid error is returned.\n\nDefaults to true if `resourceVersion=\"\"` or `resourceVersion=\"0\"` (for backward compatibility reasons) and to false otherwise.", "type": "boolean"}, "timeoutSeconds": {"description": "Timeout for the list/watch call. This limits the duration of the call, regardless of any activity or inactivity.", "type": "integer", "format": "int64"}, "watch": {"description": "Watch for changes to the described resources and return them as a stream of add, update, and remove notifications. Specify resourceVersion.", "type": "boolean"}}}, "v1.ManagedFieldsEntry": {"description": "ManagedFieldsEntry is a workflow-id, a FieldSet and the group version of the resource that the fieldset applies to.", "type": "object", "properties": {"apiVersion": {"description": "APIVersion defines the version of this resource that this field set applies to. The format is \"group/version\" just like the top-level APIVersion field. It is necessary to track the version of a field set because it cannot be automatically converted.", "type": "string"}, "fieldsType": {"description": "FieldsType is the discriminator for the different fields format and version. There is currently only one possible value: \"FieldsV1\"", "type": "string"}, "fieldsV1": {"description": "FieldsV1 holds the first JSON version format as described in the \"FieldsV1\" type.", "$ref": "#/definitions/v1.FieldsV1"}, "manager": {"description": "Manager is an identifier of the workflow managing these fields.", "type": "string"}, "operation": {"description": "Operation is the type of operation which lead to this ManagedFieldsEntry being created. The only valid values for this field are 'Apply' and 'Update'.", "type": "string"}, "subresource": {"description": "Subresource is the name of the subresource used to update that object, or empty string if the object was updated through the main resource. The value of this field is used to distinguish between managers, even if they share the same name. For example, a status update will be distinct from a regular update using the same manager name. Note that the APIVersion field is not related to the Subresource field and it always corresponds to the version of the main resource.", "type": "string"}, "time": {"description": "Time is the timestamp of when the ManagedFields entry was added. The timestamp will also be updated if a field is added, the manager changes any of the owned fields value or removes a field. The timestamp does not update when a field is removed from the entry because another manager took it over.", "$ref": "#/definitions/v1.Time"}}}, "v1.MicroTime": {"description": "MicroTime is version of Time with microsecond level precision.", "type": "string", "format": "date-time"}, "v1.ObjectMeta": {"description": "ObjectMeta is metadata that all persisted resources must have, which includes all objects users must create.", "type": "object", "properties": {"annotations": {"description": "Annotations is an unstructured key value map stored with a resource that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations", "type": "object", "additionalProperties": {"type": "string", "default": ""}}, "creationTimestamp": {"description": "CreationTimestamp is a timestamp representing the server time when this object was created. It is not guaranteed to be set in happens-before order across separate operations. Clients may not set this value. It is represented in RFC3339 form and is in UTC.\n\nPopulated by the system. Read-only. Null for lists. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata", "$ref": "#/definitions/v1.Time"}, "deletionGracePeriodSeconds": {"description": "Number of seconds allowed for this object to gracefully terminate before it will be removed from the system. Only set when deletionTimestamp is also set. May only be shortened. Read-only.", "type": "integer", "format": "int64"}, "deletionTimestamp": {"description": "DeletionTimestamp is RFC 3339 date and time at which this resource will be deleted. This field is set by the server when a graceful deletion is requested by the user, and is not directly settable by a client. The resource is expected to be deleted (no longer visible from resource lists, and not reachable by name) after the time in this field, once the finalizers list is empty. As long as the finalizers list contains items, deletion is blocked. Once the deletionTimestamp is set, this value may not be unset or be set further into the future, although it may be shortened or the resource may be deleted prior to this time. For example, a user may request that a pod is deleted in 30 seconds. The Kubelet will react by sending a graceful termination signal to the containers in the pod. After that 30 seconds, the Kubelet will send a hard termination signal (SIGKILL) to the container and after cleanup, remove the pod from the API. In the presence of network partitions, this object may still exist after this timestamp, until an administrator or automated process can determine the resource is fully terminated. If not set, graceful deletion of the object has not been requested.\n\nPopulated by the system when a graceful deletion is requested. Read-only. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata", "$ref": "#/definitions/v1.Time"}, "finalizers": {"description": "Must be empty before the object is deleted from the registry. Each entry is an identifier for the responsible component that will remove the entry from the list. If the deletionTimestamp of the object is non-nil, entries in this list can only be removed. Finalizers may be processed and removed in any order.  Order is NOT enforced because it introduces significant risk of stuck finalizers. finalizers is a shared field, any actor with permission can reorder it. If the finalizer list is processed in order, then this can lead to a situation in which the component responsible for the first finalizer in the list is waiting for a signal (field value, external system, or other) produced by a component responsible for a finalizer later in the list, resulting in a deadlock. Without enforced ordering finalizers are free to order amongst themselves and are not vulnerable to ordering changes in the list.", "type": "array", "items": {"type": "string", "default": ""}, "x-kubernetes-list-type": "set", "x-kubernetes-patch-strategy": "merge"}, "generateName": {"description": "GenerateName is an optional prefix, used by the server, to generate a unique name ONLY IF the Name field has not been provided. If this field is used, the name returned to the client will be different than the name passed. This value will also be combined with a unique suffix. The provided value has the same validation rules as the Name field, and may be truncated by the length of the suffix required to make the value unique on the server.\n\nIf this field is specified and the generated name exists, the server will return a 409.\n\nApplied only if Name is not specified. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#idempotency", "type": "string"}, "generation": {"description": "A sequence number representing a specific generation of the desired state. Populated by the system. Read-only.", "type": "integer", "format": "int64"}, "labels": {"description": "Map of string keys and values that can be used to organize and categorize (scope and select) objects. May match selectors of replication controllers and services. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels", "type": "object", "additionalProperties": {"type": "string", "default": ""}}, "managedFields": {"description": "ManagedFields maps workflow-id and version to the set of fields that are managed by that workflow. This is mostly for internal housekeeping, and users typically shouldn't need to set or understand this field. A workflow can be the user's name, a controller's name, or the name of a specific apply path like \"ci-cd\". The set of fields is always in the version that the workflow used when modifying the object.", "type": "array", "items": {"default": {}, "$ref": "#/definitions/v1.ManagedFieldsEntry"}, "x-kubernetes-list-type": "atomic"}, "name": {"description": "Name must be unique within a namespace. Is required when creating resources, although some resources may allow a client to request the generation of an appropriate name automatically. Name is primarily intended for creation idempotence and configuration definition. Cannot be updated. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names", "type": "string"}, "namespace": {"description": "Namespace defines the space within which each name must be unique. An empty namespace is equivalent to the \"default\" namespace, but \"default\" is the canonical representation. Not all objects are required to be scoped to a namespace - the value of this field for those objects will be empty.\n\nMust be a DNS_LABEL. Cannot be updated. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces", "type": "string"}, "ownerReferences": {"description": "List of objects depended by this object. If ALL objects in the list have been deleted, this object will be garbage collected. If this object is managed by a controller, then an entry in this list will point to this controller, with the controller field set to true. There cannot be more than one managing controller.", "type": "array", "items": {"default": {}, "$ref": "#/definitions/v1.OwnerReference"}, "x-kubernetes-list-map-keys": ["uid"], "x-kubernetes-list-type": "map", "x-kubernetes-patch-merge-key": "uid", "x-kubernetes-patch-strategy": "merge"}, "resourceVersion": {"description": "An opaque value that represents the internal version of this object that can be used by clients to determine when objects have changed. May be used for optimistic concurrency, change detection, and the watch operation on a resource or set of resources. Clients must treat these values as opaque and passed unmodified back to the server. They may only be valid for a particular resource or set of resources.\n\nPopulated by the system. Read-only. Value must be treated as opaque by clients and . More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency", "type": "string"}, "selfLink": {"description": "Deprecated: selfLink is a legacy read-only field that is no longer populated by the system.", "type": "string"}, "uid": {"description": "UID is the unique in time and space value for this object. It is typically generated by the server on successful creation of a resource and is not allowed to change on PUT operations.\n\nPopulated by the system. Read-only. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids", "type": "string"}}}, "v1.OwnerReference": {"description": "OwnerReference contains enough information to let you identify an owning object. An owning object must be in the same namespace as the dependent, or be cluster-scoped, so there is no namespace field.", "type": "object", "required": ["apiVersion", "kind", "name", "uid"], "properties": {"apiVersion": {"description": "API version of the referent.", "type": "string", "default": ""}, "blockOwnerDeletion": {"description": "If true, AND if the owner has the \"foregroundDeletion\" finalizer, then the owner cannot be deleted from the key-value store until this reference is removed. See https://kubernetes.io/docs/concepts/architecture/garbage-collection/#foreground-deletion for how the garbage collector interacts with this field and enforces the foreground deletion. Defaults to false. To set this field, a user needs \"delete\" permission of the owner, otherwise 422 (Unprocessable Entity) will be returned.", "type": "boolean"}, "controller": {"description": "If true, this reference points to the managing controller.", "type": "boolean"}, "kind": {"description": "Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string", "default": ""}, "name": {"description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names", "type": "string", "default": ""}, "uid": {"description": "UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids", "type": "string", "default": ""}}, "x-kubernetes-map-type": "atomic"}, "v1.PartialObjectMetadata": {"description": "PartialObjectMetadata is a generic representation of any object with ObjectMeta. It allows clients to get access to a particular ObjectMeta schema without knowing the details of the version.", "type": "object", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"description": "Standard object's metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata", "default": {}, "$ref": "#/definitions/v1.ObjectMeta"}}}, "v1.PartialObjectMetadataList": {"description": "PartialObjectMetadataList contains a list of objects containing only their metadata", "type": "object", "required": ["items"], "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "items": {"description": "items contains each of the included items.", "type": "array", "items": {"default": {}, "$ref": "#/definitions/v1.PartialObjectMetadata"}}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"description": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "default": {}, "$ref": "#/definitions/v1.ListMeta"}}}, "v1.Patch": {"description": "Patch is provided to give a concrete name and type to the Kubernetes PATCH request body.", "type": "object"}, "v1.PatchOptions": {"description": "PatchOptions may be provided when patching an API object. PatchOptions is meant to be a superset of UpdateOptions.", "type": "object", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "dryRun": {"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "type": "array", "items": {"type": "string", "default": ""}, "x-kubernetes-list-type": "atomic"}, "fieldManager": {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint. This field is required for apply requests (application/apply-patch) but optional for non-apply patch types (JsonPatch, MergePatch, StrategicMergePatch).", "type": "string"}, "fieldValidation": {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "type": "string"}, "force": {"description": "Force is going to \"force\" Apply requests. It means user will re-acquire conflicting fields owned by other people. Force flag must be unset for non-apply patch requests.", "type": "boolean"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}}}, "v1.Preconditions": {"description": "Preconditions must be fulfilled before an operation (update, delete, etc.) is carried out.", "type": "object", "properties": {"resourceVersion": {"description": "Specifies the target ResourceVersion", "type": "string"}, "uid": {"description": "Specifies the target UID.", "type": "string"}}}, "v1.RootPaths": {"description": "RootPaths lists the paths available at root. For example: \"/healthz\", \"/apis\".", "type": "object", "required": ["paths"], "properties": {"paths": {"description": "paths are the paths available at root.", "type": "array", "items": {"type": "string", "default": ""}, "x-kubernetes-list-type": "atomic"}}}, "v1.ServerAddressByClientCIDR": {"description": "ServerAddressByClientCIDR helps the client to determine the server address that they should use, depending on the clientCIDR that they match.", "type": "object", "required": ["clientCIDR", "serverAddress"], "properties": {"clientCIDR": {"description": "The CIDR with which clients can match their IP to figure out the server address that they should use.", "type": "string", "default": ""}, "serverAddress": {"description": "Address of this server, suitable for a client that matches the above CIDR. This can be a hostname, hostname:port, IP or IP:port.", "type": "string", "default": ""}}}, "v1.Status": {"description": "Status is a return value for calls that don't return other objects.", "type": "object", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "code": {"description": "Suggested HTTP return code for this status, 0 if not set.", "type": "integer", "format": "int32"}, "details": {"description": "Extended data associated with the reason.  Each reason may define its own extended details. This field is optional and the data returned is not guaranteed to conform to any schema except that defined by the reason type.", "$ref": "#/definitions/v1.StatusDetails", "x-kubernetes-list-type": "atomic"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "message": {"description": "A human-readable description of the status of this operation.", "type": "string"}, "metadata": {"description": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "default": {}, "$ref": "#/definitions/v1.ListMeta"}, "reason": {"description": "A machine-readable description of why this operation is in the \"Failure\" status. If this value is empty there is no information available. A Reason clarifies an HTTP status code but does not override it.", "type": "string"}, "status": {"description": "Status of the operation. One of: \"Success\" or \"Failure\". More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#spec-and-status", "type": "string"}}}, "v1.StatusCause": {"description": "StatusCause provides more information about an api.Status failure, including cases when multiple errors are encountered.", "type": "object", "properties": {"field": {"description": "The field of the resource that has caused this error, as named by its JSON serialization. May include dot and postfix notation for nested attributes. Arrays are zero-indexed.  Fields may appear more than once in an array of causes due to fields having multiple errors. Optional.\n\nExamples:\n  \"name\" - the field \"name\" on the current resource\n  \"items[0].name\" - the field \"name\" on the first array entry in \"items\"", "type": "string"}, "message": {"description": "A human-readable description of the cause of the error.  This field may be presented as-is to a reader.", "type": "string"}, "reason": {"description": "A machine-readable description of the cause of the error. If this value is empty there is no information available.", "type": "string"}}}, "v1.StatusDetails": {"description": "StatusDetails is a set of additional properties that MAY be set by the server to provide additional information about a response. The Reason field of a Status object defines what attributes will be set. Clients must ignore fields that do not match the defined type of each attribute, and should assume that any attribute may be empty, invalid, or under defined.", "type": "object", "properties": {"causes": {"description": "The Causes array includes more details associated with the StatusReason failure. Not all StatusReasons may provide detailed causes.", "type": "array", "items": {"default": {}, "$ref": "#/definitions/v1.StatusCause"}, "x-kubernetes-list-type": "atomic"}, "group": {"description": "The group attribute of the resource associated with the status StatusReason.", "type": "string"}, "kind": {"description": "The kind attribute of the resource associated with the status StatusReason. On some operations may differ from the requested resource Kind. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "name": {"description": "The name attribute of the resource associated with the status StatusReason (when there is a single name which can be described).", "type": "string"}, "retryAfterSeconds": {"description": "If specified, the time in seconds before the operation should be retried. Some errors may indicate the client must take an alternate action - for those errors this field may indicate how long to wait before taking the alternate action.", "type": "integer", "format": "int32"}, "uid": {"description": "UID of the resource. (when there is a single resource which can be described). More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids", "type": "string"}}}, "v1.Table": {"description": "Table is a tabular representation of a set of API resources. The server transforms the object into a set of preferred columns for quickly reviewing the objects.", "type": "object", "required": ["columnDefinitions", "rows"], "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "columnDefinitions": {"description": "columnDefinitions describes each column in the returned items array. The number of cells per row will always match the number of column definitions.", "type": "array", "items": {"default": {}, "$ref": "#/definitions/v1.TableColumnDefinition"}, "x-kubernetes-list-type": "atomic"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"description": "Standard list metadata. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "default": {}, "$ref": "#/definitions/v1.ListMeta"}, "rows": {"description": "rows is the list of items in the table.", "type": "array", "items": {"default": {}, "$ref": "#/definitions/v1.TableRow"}, "x-kubernetes-list-type": "atomic"}}}, "v1.TableColumnDefinition": {"description": "TableColumnDefinition contains information about a column returned in the Table.", "type": "object", "required": ["name", "type", "format", "description", "priority"], "properties": {"description": {"description": "description is a human readable description of this column.", "type": "string", "default": ""}, "format": {"description": "format is an optional OpenAPI type modifier for this column. A format modifies the type and imposes additional rules, like date or time formatting for a string. The 'name' format is applied to the primary identifier column which has type 'string' to assist in clients identifying column is the resource name. See https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#data-types for more.", "type": "string", "default": ""}, "name": {"description": "name is a human readable name for the column.", "type": "string", "default": ""}, "priority": {"description": "priority is an integer defining the relative importance of this column compared to others. Lower numbers are considered higher priority. Columns that may be omitted in limited space scenarios should be given a higher priority.", "type": "integer", "format": "int32", "default": 0}, "type": {"description": "type is an OpenAPI type definition for this column, such as number, integer, string, or array. See https://github.com/OAI/OpenAPI-Specification/blob/master/versions/2.0.md#data-types for more.", "type": "string", "default": ""}}}, "v1.TableOptions": {"description": "TableOptions are used when a Table is requested by the caller.", "type": "object", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "includeObject": {"description": "includeObject decides whether to include each object along with its columnar information. Specifying \"None\" will return no object, specifying \"Object\" will return the full object contents, and specifying \"Metadata\" (the default) will return the object's metadata in the PartialObjectMetadata kind in version v1beta1 of the meta.k8s.io API group.", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}}}, "v1.TableRow": {"description": "TableRow is an individual row in a table.", "type": "object", "required": ["cells"], "properties": {"cells": {"description": "cells will be as wide as the column definitions array and may contain strings, numbers (float64 or int64), booleans, simple maps, lists, or null. See the type field of the column definition for a more detailed description.", "type": "array", "items": {"type": "object"}, "x-kubernetes-list-type": "atomic"}, "conditions": {"description": "conditions describe additional status of a row that are relevant for a human user. These conditions apply to the row, not to the object, and will be specific to table output. The only defined condition type is 'Completed', for a row that indicates a resource that has run to completion and can be given less visual priority.", "type": "array", "items": {"default": {}, "$ref": "#/definitions/v1.TableRowCondition"}, "x-kubernetes-list-type": "atomic"}, "object": {"description": "This field contains the requested additional information about each object based on the includeObject policy when requesting the Table. If \"None\", this field is empty, if \"Object\" this will be the default serialization of the object for the current API version, and if \"Metadata\" (the default) will contain the object metadata. Check the returned kind and apiVersion of the object before parsing. The media type of the object will always match the enclosing list - if this as a JSON table, these will be JSON encoded objects.", "$ref": "#/definitions/k8s.io.apimachinery.pkg.runtime.RawExtension"}}}, "v1.TableRowCondition": {"description": "TableRowCondition allows a row to be marked with additional information.", "type": "object", "required": ["type", "status"], "properties": {"message": {"description": "Human readable message indicating details about last transition.", "type": "string"}, "reason": {"description": "(brief) machine readable reason for the condition's last transition.", "type": "string"}, "status": {"description": "Status of the condition, one of True, False, Unknown.", "type": "string", "default": ""}, "type": {"description": "Type of row condition. The only defined value is 'Completed' indicating that the object this row represents has reached a completed state and may be given less visual priority than other rows. Clients are not required to honor any conditions but should be consistent where possible about handling the conditions.", "type": "string", "default": ""}}}, "v1.Time": {"description": "Time is a wrapper around time.Time which supports correct marshaling to YAML and JSON.  Wrappers are provided for many of the factory methods that the time package offers.", "type": "string", "format": "date-time"}, "v1.Timestamp": {"description": "Timestamp is a struct that is equivalent to Time, but intended for protobuf marshalling/unmarshalling. It is generated into a serialization that matches Time. Do not use in Go structs.", "type": "object", "required": ["seconds", "nanos"], "properties": {"nanos": {"description": "Non-negative fractions of a second at nanosecond resolution. Negative second values with fractions must still have non-negative nanos values that count forward in time. Must be from 0 to 999,999,999 inclusive. This field may be limited in precision depending on context.", "type": "integer", "format": "int32", "default": 0}, "seconds": {"description": "Represents seconds of UTC time since Unix epoch 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.", "type": "integer", "format": "int64", "default": 0}}}, "v1.TypeMeta": {"description": "TypeMeta describes an individual object in an API response or request with strings representing the type of the object and its API schema version. Structures that are versioned or persisted should inline TypeMeta.", "type": "object", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}}}, "v1.UpdateOptions": {"description": "UpdateOptions may be provided when updating an API object. All fields in UpdateOptions should also be present in PatchOptions.", "type": "object", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "dryRun": {"description": "When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed", "type": "array", "items": {"type": "string", "default": ""}, "x-kubernetes-list-type": "atomic"}, "fieldManager": {"description": "fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint.", "type": "string"}, "fieldValidation": {"description": "fieldValidation instructs the server on how to handle objects in the request (POST/PUT/PATCH) containing unknown or duplicate fields. Valid values are: - Ignore: This will ignore any unknown fields that are silently dropped from the object, and will ignore all but the last duplicate field that the decoder encounters. This is the default behavior prior to v1.23. - Warn: This will send a warning via the standard warning response header for each unknown field that is dropped from the object, and for each duplicate field that is encountered. The request will still succeed if there are no other errors, and will only persist the last of any duplicate fields. This is the default in v1.23+ - Strict: This will fail the request with a BadRequest error if any unknown fields would be dropped from the object, or if any duplicate fields are present. The error returned from the server will contain all unknown and duplicate fields encountered.", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}}}, "v1.WatchEvent": {"description": "Event represents a single event to a watched resource.", "type": "object", "required": ["type", "object"], "properties": {"object": {"description": "Object is:\n * If Type is Added or Modified: the new state of the object.\n * If Type is Deleted: the state of the object immediately before deletion.\n * If Type is Error: *Status is recommended; other types may make sense\n   depending on context.", "$ref": "#/definitions/k8s.io.apimachinery.pkg.runtime.RawExtension"}, "type": {"type": "string", "default": ""}}}, "v2beta1.JobCondition": {"description": "JobCondition describes the state of the job at a certain point.", "type": "object", "required": ["type", "status"], "properties": {"lastTransitionTime": {"description": "Last time the condition transitioned from one status to another.", "$ref": "#/definitions/v1.Time"}, "lastUpdateTime": {"description": "The last time this condition was updated.", "$ref": "#/definitions/v1.Time"}, "message": {"description": "A human-readable message indicating details about the transition.", "type": "string"}, "reason": {"description": "The reason for the condition's last transition.", "type": "string"}, "status": {"description": "status of the condition, one of True, False, Unknown.", "type": "string", "default": ""}, "type": {"description": "type of job condition.", "type": "string", "default": ""}}}, "v2beta1.JobStatus": {"description": "JobStatus represents the current observed state of the training Job.", "type": "object", "properties": {"completionTime": {"description": "Represents time when the job was completed. It is not guaranteed to be set in happens-before order across separate operations. It is represented in RFC3339 form and is in UTC.", "$ref": "#/definitions/v1.Time"}, "conditions": {"description": "conditions is a list of current observed job conditions.", "type": "array", "items": {"default": {}, "$ref": "#/definitions/v2beta1.JobCondition"}, "x-kubernetes-list-map-keys": ["type"], "x-kubernetes-list-type": "map"}, "lastReconcileTime": {"description": "Represents last time when the job was reconciled. It is not guaranteed to be set in happens-before order across separate operations. It is represented in RFC3339 form and is in UTC.", "$ref": "#/definitions/v1.Time"}, "replicaStatuses": {"description": "replicaStatuses is map of ReplicaType and ReplicaStatus, specifies the status of each replica.", "type": "object", "additionalProperties": {"$ref": "#/definitions/v2beta1.ReplicaStatus"}}, "startTime": {"description": "Represents time when the job was acknowledged by the job controller. It is not guaranteed to be set in happens-before order across separate operations. It is represented in RFC3339 form and is in UTC.", "$ref": "#/definitions/v1.Time"}}}, "v2beta1.MPIJob": {"type": "object", "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"default": {}, "$ref": "#/definitions/v1.ObjectMeta"}, "spec": {"default": {}, "$ref": "#/definitions/v2beta1.MPIJobSpec"}, "status": {"default": {}, "$ref": "#/definitions/v2beta1.JobStatus"}}}, "v2beta1.MPIJobList": {"type": "object", "required": ["metadata", "items"], "properties": {"apiVersion": {"description": "APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources", "type": "string"}, "items": {"type": "array", "items": {"default": {}, "$ref": "#/definitions/v2beta1.MPIJob"}}, "kind": {"description": "Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds", "type": "string"}, "metadata": {"default": {}, "$ref": "#/definitions/v1.ListMeta"}}}, "v2beta1.MPIJobSpec": {"type": "object", "required": ["mpiReplicaSpecs"], "properties": {"launcherCreationPolicy": {"description": "launcherCreationPolicy if WaitForWorkersReady, the launcher is created only after all workers are in Ready state. Defaults to AtStartup.", "type": "string"}, "mpiImplementation": {"description": "MPIImplementation is the MPI implementation. Options are \"OpenMPI\" (default), \"Intel\" and \"MPICH\".", "type": "string"}, "mpiReplicaSpecs": {"description": "MPIReplicaSpecs contains maps from `MPIReplicaType` to `ReplicaSpec` that specify the MPI replicas to run.", "type": "object", "additionalProperties": {"$ref": "#/definitions/v2beta1.ReplicaSpec"}}, "runLauncherAsWorker": {"description": "RunLauncherAsWorker indicates whether to run worker process in launcher Defaults to false.", "type": "boolean"}, "runPolicy": {"description": "RunPolicy encapsulates various runtime policies of the job.", "default": {}, "$ref": "#/definitions/v2beta1.RunPolicy"}, "slotsPerWorker": {"description": "Specifies the number of slots per worker used in hostfile. Defaults to 1.", "type": "integer", "format": "int32"}, "sshAuthMountPath": {"description": "SSHAuthMountPath is the directory where SSH keys are mounted. Defaults to \"/root/.ssh\".", "type": "string"}}}, "v2beta1.ReplicaSpec": {"description": "ReplicaSpec is a description of the replica", "type": "object", "properties": {"replicas": {"description": "Replicas is the desired number of replicas of the given template. If unspecified, defaults to 1.", "type": "integer", "format": "int32"}, "restartPolicy": {"description": "Restart policy for all replicas within the job. One of Always, OnFailure, Never and ExitCode. Default to Never.", "type": "string"}, "template": {"description": "Template is the object that describes the pod that will be created for this replica. RestartPolicy in PodTemplateSpec will be overide by RestartPolicy in ReplicaSpec", "default": {}, "$ref": "#/definitions/v1.PodTemplateSpec"}}}, "v2beta1.ReplicaStatus": {"description": "ReplicaStatus represents the current observed state of the replica.", "type": "object", "properties": {"active": {"description": "The number of actively running pods.", "type": "integer", "format": "int32"}, "failed": {"description": "The number of pods which reached phase failed.", "type": "integer", "format": "int32"}, "labelSelector": {"description": "Deprecated: Use selector instead", "$ref": "#/definitions/v1.LabelSelector"}, "selector": {"description": "A selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty selector matches all objects. A null selector matches no objects.", "type": "string"}, "succeeded": {"description": "The number of pods which reached phase succeeded.", "type": "integer", "format": "int32"}}}, "v2beta1.RunPolicy": {"description": "RunPolicy encapsulates various runtime policies of the distributed training job, for example how to clean up resources and how long the job can stay active.", "type": "object", "properties": {"activeDeadlineSeconds": {"description": "Specifies the duration in seconds relative to the startTime that the job may be active before the system tries to terminate it; value must be positive integer.", "type": "integer", "format": "int64"}, "backoffLimit": {"description": "Optional number of retries before marking this job failed.", "type": "integer", "format": "int32"}, "cleanPodPolicy": {"description": "CleanPodPolicy defines the policy to kill pods after the job completes. De<PERSON><PERSON> to Running.", "type": "string"}, "managedBy": {"description": "ManagedBy is used to indicate the controller or entity that manages a MPIJob. The value must be either empty, 'kubeflow.org/mpi-operator' or 'kueue.x-k8s.io/multikueue'. The mpi-operator reconciles a MPIJob which doesn't have this field at all or the field value is the reserved string 'kubeflow.org/mpi-operator', but delegates reconciling the MPIJob with 'kueue.x-k8s.io/multikueue' to the Kueue. The field is immutable.", "type": "string"}, "schedulingPolicy": {"description": "SchedulingPolicy defines the policy related to scheduling, e.g. gang-scheduling", "$ref": "#/definitions/v2beta1.SchedulingPolicy"}, "suspend": {"description": "suspend specifies whether the MPIJob controller should create Pods or not. If a MPIJob is created with suspend set to true, no Pods are created by the MPIJob controller. If a MPIJob is suspended after creation (i.e. the flag goes from false to true), the MPIJob controller will delete all active Pods and PodGroups associated with this MPIJob. Also, it will suspend the Launcher Job. Users must design their workload to gracefully handle this. Suspending a Job will reset the StartTime field of the MPIJob.\n\nDefaults to false.", "type": "boolean"}, "ttlSecondsAfterFinished": {"description": "TTLSecondsAfterFinished is the TTL to clean up jobs. It may take extra ReconcilePeriod seconds for the cleanup, since reconcile gets called periodically. Default to infinite.", "type": "integer", "format": "int32"}}}, "v2beta1.SchedulingPolicy": {"description": "SchedulingPolicy encapsulates various scheduling policies of the distributed training job, for example `minAvailable` for gang-scheduling. Now, it supports only for volcano and scheduler-plugins.", "type": "object", "properties": {"minAvailable": {"description": "MinAvailable defines the minimal number of member to run the PodGroup. If the gang-scheduling isn't empty, input is passed to `.spec.minMember` in PodGroup. Note that, when using this field, you need to make sure the application supports resizing (e.g., Elastic Horovod).\n\nIf not set, it defaults to the number of workers.", "type": "integer", "format": "int32"}, "minResources": {"description": "MinResources defines the minimal resources of members to run the PodGroup. If the gang-scheduling isn't empty, input is passed to `.spec.minResources` in PodGroup for scheduler-plugins.", "type": "object", "additionalProperties": {"$ref": "#/definitions/resource.Quantity"}}, "priorityClass": {"description": "PriorityClass defines the PodGroup's PriorityClass. If the gang-scheduling is set to the volcano, input is passed to `.spec.priorityClassName` in PodGroup for volcano, and if it is set to the scheduler-plugins, input isn't passed to PodGroup for scheduler-plugins.", "type": "string"}, "queue": {"description": "Queue defines the queue name to allocate resource for PodGroup. If the gang-scheduling is set to the volcano, input is passed to `.spec.queue` in PodGroup for the volcano, and if it is set to the scheduler-plugins, input isn't passed to PodGroup.", "type": "string"}, "scheduleTimeoutSeconds": {"description": "SchedulerTimeoutSeconds defines the maximal time of members to wait before run the PodGroup. If the gang-scheduling is set to the scheduler-plugins, input is passed to `.spec.scheduleTimeoutSeconds` in PodGroup for the scheduler-plugins, and if it is set to the volcano, input isn't passed to PodGroup.", "type": "integer", "format": "int32"}}}}}