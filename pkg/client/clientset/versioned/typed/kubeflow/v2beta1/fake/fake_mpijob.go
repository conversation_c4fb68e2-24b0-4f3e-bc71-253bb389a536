// Copyright 2025 The Kubeflow Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"
	json "encoding/json"
	"fmt"

	v2beta1 "github.com/kubeflow/mpi-operator/pkg/apis/kubeflow/v2beta1"
	kubeflowv2beta1 "github.com/kubeflow/mpi-operator/pkg/client/applyconfiguration/kubeflow/v2beta1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeMPIJobs implements MPIJobInterface
type FakeMPIJobs struct {
	Fake *FakeKubeflowV2beta1
	ns   string
}

var mpijobsResource = v2beta1.SchemeGroupVersion.WithResource("mpijobs")

var mpijobsKind = v2beta1.SchemeGroupVersion.WithKind("MPIJob")

// Get takes name of the mPIJob, and returns the corresponding mPIJob object, and an error if there is any.
func (c *FakeMPIJobs) Get(ctx context.Context, name string, options v1.GetOptions) (result *v2beta1.MPIJob, err error) {
	emptyResult := &v2beta1.MPIJob{}
	obj, err := c.Fake.
		Invokes(testing.NewGetActionWithOptions(mpijobsResource, c.ns, name, options), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v2beta1.MPIJob), err
}

// List takes label and field selectors, and returns the list of MPIJobs that match those selectors.
func (c *FakeMPIJobs) List(ctx context.Context, opts v1.ListOptions) (result *v2beta1.MPIJobList, err error) {
	emptyResult := &v2beta1.MPIJobList{}
	obj, err := c.Fake.
		Invokes(testing.NewListActionWithOptions(mpijobsResource, mpijobsKind, c.ns, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v2beta1.MPIJobList{ListMeta: obj.(*v2beta1.MPIJobList).ListMeta}
	for _, item := range obj.(*v2beta1.MPIJobList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested mPIJobs.
func (c *FakeMPIJobs) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchActionWithOptions(mpijobsResource, c.ns, opts))

}

// Create takes the representation of a mPIJob and creates it.  Returns the server's representation of the mPIJob, and an error, if there is any.
func (c *FakeMPIJobs) Create(ctx context.Context, mPIJob *v2beta1.MPIJob, opts v1.CreateOptions) (result *v2beta1.MPIJob, err error) {
	emptyResult := &v2beta1.MPIJob{}
	obj, err := c.Fake.
		Invokes(testing.NewCreateActionWithOptions(mpijobsResource, c.ns, mPIJob, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v2beta1.MPIJob), err
}

// Update takes the representation of a mPIJob and updates it. Returns the server's representation of the mPIJob, and an error, if there is any.
func (c *FakeMPIJobs) Update(ctx context.Context, mPIJob *v2beta1.MPIJob, opts v1.UpdateOptions) (result *v2beta1.MPIJob, err error) {
	emptyResult := &v2beta1.MPIJob{}
	obj, err := c.Fake.
		Invokes(testing.NewUpdateActionWithOptions(mpijobsResource, c.ns, mPIJob, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v2beta1.MPIJob), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeMPIJobs) UpdateStatus(ctx context.Context, mPIJob *v2beta1.MPIJob, opts v1.UpdateOptions) (result *v2beta1.MPIJob, err error) {
	emptyResult := &v2beta1.MPIJob{}
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceActionWithOptions(mpijobsResource, "status", c.ns, mPIJob, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v2beta1.MPIJob), err
}

// Delete takes name of the mPIJob and deletes it. Returns an error if one occurs.
func (c *FakeMPIJobs) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteActionWithOptions(mpijobsResource, c.ns, name, opts), &v2beta1.MPIJob{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeMPIJobs) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionActionWithOptions(mpijobsResource, c.ns, opts, listOpts)

	_, err := c.Fake.Invokes(action, &v2beta1.MPIJobList{})
	return err
}

// Patch applies the patch and returns the patched mPIJob.
func (c *FakeMPIJobs) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v2beta1.MPIJob, err error) {
	emptyResult := &v2beta1.MPIJob{}
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceActionWithOptions(mpijobsResource, c.ns, name, pt, data, opts, subresources...), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v2beta1.MPIJob), err
}

// Apply takes the given apply declarative configuration, applies it and returns the applied mPIJob.
func (c *FakeMPIJobs) Apply(ctx context.Context, mPIJob *kubeflowv2beta1.MPIJobApplyConfiguration, opts v1.ApplyOptions) (result *v2beta1.MPIJob, err error) {
	if mPIJob == nil {
		return nil, fmt.Errorf("mPIJob provided to Apply must not be nil")
	}
	data, err := json.Marshal(mPIJob)
	if err != nil {
		return nil, err
	}
	name := mPIJob.Name
	if name == nil {
		return nil, fmt.Errorf("mPIJob.Name must be provided to Apply")
	}
	emptyResult := &v2beta1.MPIJob{}
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceActionWithOptions(mpijobsResource, c.ns, *name, types.ApplyPatchType, data, opts.ToPatchOptions()), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v2beta1.MPIJob), err
}

// ApplyStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating ApplyStatus().
func (c *FakeMPIJobs) ApplyStatus(ctx context.Context, mPIJob *kubeflowv2beta1.MPIJobApplyConfiguration, opts v1.ApplyOptions) (result *v2beta1.MPIJob, err error) {
	if mPIJob == nil {
		return nil, fmt.Errorf("mPIJob provided to Apply must not be nil")
	}
	data, err := json.Marshal(mPIJob)
	if err != nil {
		return nil, err
	}
	name := mPIJob.Name
	if name == nil {
		return nil, fmt.Errorf("mPIJob.Name must be provided to Apply")
	}
	emptyResult := &v2beta1.MPIJob{}
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceActionWithOptions(mpijobsResource, c.ns, *name, types.ApplyPatchType, data, opts.ToPatchOptions(), "status"), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v2beta1.MPIJob), err
}
