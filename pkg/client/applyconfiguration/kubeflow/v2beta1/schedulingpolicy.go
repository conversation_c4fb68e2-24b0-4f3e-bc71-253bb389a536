// Copyright 2025 The Kubeflow Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v2beta1

import (
	v1 "k8s.io/api/core/v1"
)

// SchedulingPolicyApplyConfiguration represents a declarative configuration of the SchedulingPolicy type for use
// with apply.
type SchedulingPolicyApplyConfiguration struct {
	MinAvailable           *int32           `json:"minAvailable,omitempty"`
	Queue                  *string          `json:"queue,omitempty"`
	MinResources           *v1.ResourceList `json:"minResources,omitempty"`
	PriorityClass          *string          `json:"priorityClass,omitempty"`
	ScheduleTimeoutSeconds *int32           `json:"scheduleTimeoutSeconds,omitempty"`
}

// SchedulingPolicyApplyConfiguration constructs a declarative configuration of the SchedulingPolicy type for use with
// apply.
func SchedulingPolicy() *SchedulingPolicyApplyConfiguration {
	return &SchedulingPolicyApplyConfiguration{}
}

// WithMinAvailable sets the MinAvailable field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MinAvailable field is set to the value of the last call.
func (b *SchedulingPolicyApplyConfiguration) WithMinAvailable(value int32) *SchedulingPolicyApplyConfiguration {
	b.MinAvailable = &value
	return b
}

// WithQueue sets the Queue field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Queue field is set to the value of the last call.
func (b *SchedulingPolicyApplyConfiguration) WithQueue(value string) *SchedulingPolicyApplyConfiguration {
	b.Queue = &value
	return b
}

// WithMinResources sets the MinResources field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MinResources field is set to the value of the last call.
func (b *SchedulingPolicyApplyConfiguration) WithMinResources(value v1.ResourceList) *SchedulingPolicyApplyConfiguration {
	b.MinResources = &value
	return b
}

// WithPriorityClass sets the PriorityClass field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the PriorityClass field is set to the value of the last call.
func (b *SchedulingPolicyApplyConfiguration) WithPriorityClass(value string) *SchedulingPolicyApplyConfiguration {
	b.PriorityClass = &value
	return b
}

// WithScheduleTimeoutSeconds sets the ScheduleTimeoutSeconds field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ScheduleTimeoutSeconds field is set to the value of the last call.
func (b *SchedulingPolicyApplyConfiguration) WithScheduleTimeoutSeconds(value int32) *SchedulingPolicyApplyConfiguration {
	b.ScheduleTimeoutSeconds = &value
	return b
}
