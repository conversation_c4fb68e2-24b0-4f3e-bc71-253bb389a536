// Copyright 2025 The Kubeflow Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v2beta1

import (
	v2beta1 "github.com/kubeflow/mpi-operator/pkg/apis/kubeflow/v2beta1"
	v1 "k8s.io/api/core/v1"
)

// ReplicaSpecApplyConfiguration represents a declarative configuration of the ReplicaSpec type for use
// with apply.
type ReplicaSpecApplyConfiguration struct {
	Replicas      *int32                 `json:"replicas,omitempty"`
	Template      *v1.PodTemplateSpec    `json:"template,omitempty"`
	RestartPolicy *v2beta1.RestartPolicy `json:"restartPolicy,omitempty"`
}

// ReplicaSpecApplyConfiguration constructs a declarative configuration of the ReplicaSpec type for use with
// apply.
func ReplicaSpec() *ReplicaSpecApplyConfiguration {
	return &ReplicaSpecApplyConfiguration{}
}

// WithReplicas sets the Replicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Replicas field is set to the value of the last call.
func (b *ReplicaSpecApplyConfiguration) WithReplicas(value int32) *ReplicaSpecApplyConfiguration {
	b.Replicas = &value
	return b
}

// WithTemplate sets the Template field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Template field is set to the value of the last call.
func (b *ReplicaSpecApplyConfiguration) WithTemplate(value v1.PodTemplateSpec) *ReplicaSpecApplyConfiguration {
	b.Template = &value
	return b
}

// WithRestartPolicy sets the RestartPolicy field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RestartPolicy field is set to the value of the last call.
func (b *ReplicaSpecApplyConfiguration) WithRestartPolicy(value v2beta1.RestartPolicy) *ReplicaSpecApplyConfiguration {
	b.RestartPolicy = &value
	return b
}
