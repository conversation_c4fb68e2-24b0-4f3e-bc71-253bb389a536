// Copyright 2025 The Kubeflow Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v2beta1

import (
	kubeflowv2beta1 "github.com/kubeflow/mpi-operator/pkg/apis/kubeflow/v2beta1"
)

// MPIJobSpecApplyConfiguration represents a declarative configuration of the MPIJobSpec type for use
// with apply.
type MPIJobSpecApplyConfiguration struct {
	SlotsPerWorker         *int32                                                          `json:"slotsPer<PERSON>orker,omitempty"`
	RunLauncherAsWorker    *bool                                                           `json:"runLauncherAsWorker,omitempty"`
	RunPolicy              *RunPolicyApplyConfiguration                                    `json:"runPolicy,omitempty"`
	MPIReplicaSpecs        map[kubeflowv2beta1.MPIReplicaType]*kubeflowv2beta1.ReplicaSpec `json:"mpiReplicaSpecs,omitempty"`
	SSHAuthMountPath       *string                                                         `json:"sshAuthMountPath,omitempty"`
	LauncherCreationPolicy *kubeflowv2beta1.LauncherCreationPolicy                         `json:"launcherCreationPolicy,omitempty"`
	MPIImplementation      *kubeflowv2beta1.MPIImplementation                              `json:"mpiImplementation,omitempty"`
}

// MPIJobSpecApplyConfiguration constructs a declarative configuration of the MPIJobSpec type for use with
// apply.
func MPIJobSpec() *MPIJobSpecApplyConfiguration {
	return &MPIJobSpecApplyConfiguration{}
}

// WithSlotsPerWorker sets the SlotsPerWorker field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SlotsPerWorker field is set to the value of the last call.
func (b *MPIJobSpecApplyConfiguration) WithSlotsPerWorker(value int32) *MPIJobSpecApplyConfiguration {
	b.SlotsPerWorker = &value
	return b
}

// WithRunLauncherAsWorker sets the RunLauncherAsWorker field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RunLauncherAsWorker field is set to the value of the last call.
func (b *MPIJobSpecApplyConfiguration) WithRunLauncherAsWorker(value bool) *MPIJobSpecApplyConfiguration {
	b.RunLauncherAsWorker = &value
	return b
}

// WithRunPolicy sets the RunPolicy field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RunPolicy field is set to the value of the last call.
func (b *MPIJobSpecApplyConfiguration) WithRunPolicy(value *RunPolicyApplyConfiguration) *MPIJobSpecApplyConfiguration {
	b.RunPolicy = value
	return b
}

// WithMPIReplicaSpecs puts the entries into the MPIReplicaSpecs field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the MPIReplicaSpecs field,
// overwriting an existing map entries in MPIReplicaSpecs field with the same key.
func (b *MPIJobSpecApplyConfiguration) WithMPIReplicaSpecs(entries map[kubeflowv2beta1.MPIReplicaType]*kubeflowv2beta1.ReplicaSpec) *MPIJobSpecApplyConfiguration {
	if b.MPIReplicaSpecs == nil && len(entries) > 0 {
		b.MPIReplicaSpecs = make(map[kubeflowv2beta1.MPIReplicaType]*kubeflowv2beta1.ReplicaSpec, len(entries))
	}
	for k, v := range entries {
		b.MPIReplicaSpecs[k] = v
	}
	return b
}

// WithSSHAuthMountPath sets the SSHAuthMountPath field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SSHAuthMountPath field is set to the value of the last call.
func (b *MPIJobSpecApplyConfiguration) WithSSHAuthMountPath(value string) *MPIJobSpecApplyConfiguration {
	b.SSHAuthMountPath = &value
	return b
}

// WithLauncherCreationPolicy sets the LauncherCreationPolicy field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the LauncherCreationPolicy field is set to the value of the last call.
func (b *MPIJobSpecApplyConfiguration) WithLauncherCreationPolicy(value kubeflowv2beta1.LauncherCreationPolicy) *MPIJobSpecApplyConfiguration {
	b.LauncherCreationPolicy = &value
	return b
}

// WithMPIImplementation sets the MPIImplementation field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MPIImplementation field is set to the value of the last call.
func (b *MPIJobSpecApplyConfiguration) WithMPIImplementation(value kubeflowv2beta1.MPIImplementation) *MPIJobSpecApplyConfiguration {
	b.MPIImplementation = &value
	return b
}
