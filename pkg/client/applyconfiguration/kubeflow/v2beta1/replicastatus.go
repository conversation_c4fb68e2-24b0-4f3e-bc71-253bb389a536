// Copyright 2025 The Kubeflow Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v2beta1

import (
	v1 "k8s.io/client-go/applyconfigurations/meta/v1"
)

// ReplicaStatusApplyConfiguration represents a declarative configuration of the ReplicaStatus type for use
// with apply.
type ReplicaStatusApplyConfiguration struct {
	Active        *int32                              `json:"active,omitempty"`
	Succeeded     *int32                              `json:"succeeded,omitempty"`
	Failed        *int32                              `json:"failed,omitempty"`
	LabelSelector *v1.LabelSelectorApplyConfiguration `json:"labelSelector,omitempty"`
	Selector      *string                             `json:"selector,omitempty"`
}

// ReplicaStatusApplyConfiguration constructs a declarative configuration of the ReplicaStatus type for use with
// apply.
func ReplicaStatus() *ReplicaStatusApplyConfiguration {
	return &ReplicaStatusApplyConfiguration{}
}

// WithActive sets the Active field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Active field is set to the value of the last call.
func (b *ReplicaStatusApplyConfiguration) WithActive(value int32) *ReplicaStatusApplyConfiguration {
	b.Active = &value
	return b
}

// WithSucceeded sets the Succeeded field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Succeeded field is set to the value of the last call.
func (b *ReplicaStatusApplyConfiguration) WithSucceeded(value int32) *ReplicaStatusApplyConfiguration {
	b.Succeeded = &value
	return b
}

// WithFailed sets the Failed field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Failed field is set to the value of the last call.
func (b *ReplicaStatusApplyConfiguration) WithFailed(value int32) *ReplicaStatusApplyConfiguration {
	b.Failed = &value
	return b
}

// WithLabelSelector sets the LabelSelector field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the LabelSelector field is set to the value of the last call.
func (b *ReplicaStatusApplyConfiguration) WithLabelSelector(value *v1.LabelSelectorApplyConfiguration) *ReplicaStatusApplyConfiguration {
	b.LabelSelector = value
	return b
}

// WithSelector sets the Selector field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Selector field is set to the value of the last call.
func (b *ReplicaStatusApplyConfiguration) WithSelector(value string) *ReplicaStatusApplyConfiguration {
	b.Selector = &value
	return b
}
