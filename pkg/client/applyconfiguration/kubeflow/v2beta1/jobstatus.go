// Copyright 2025 The Kubeflow Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v2beta1

import (
	kubeflowv2beta1 "github.com/kubeflow/mpi-operator/pkg/apis/kubeflow/v2beta1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// JobStatusApplyConfiguration represents a declarative configuration of the JobStatus type for use
// with apply.
type JobStatusApplyConfiguration struct {
	Conditions        []JobConditionApplyConfiguration                                  `json:"conditions,omitempty"`
	ReplicaStatuses   map[kubeflowv2beta1.MPIReplicaType]*kubeflowv2beta1.ReplicaStatus `json:"replicaStatuses,omitempty"`
	StartTime         *v1.Time                                                          `json:"startTime,omitempty"`
	CompletionTime    *v1.Time                                                          `json:"completionTime,omitempty"`
	LastReconcileTime *v1.Time                                                          `json:"lastReconcileTime,omitempty"`
}

// JobStatusApplyConfiguration constructs a declarative configuration of the JobStatus type for use with
// apply.
func JobStatus() *JobStatusApplyConfiguration {
	return &JobStatusApplyConfiguration{}
}

// WithConditions adds the given value to the Conditions field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Conditions field.
func (b *JobStatusApplyConfiguration) WithConditions(values ...*JobConditionApplyConfiguration) *JobStatusApplyConfiguration {
	for i := range values {
		if values[i] == nil {
			panic("nil value passed to WithConditions")
		}
		b.Conditions = append(b.Conditions, *values[i])
	}
	return b
}

// WithReplicaStatuses puts the entries into the ReplicaStatuses field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the ReplicaStatuses field,
// overwriting an existing map entries in ReplicaStatuses field with the same key.
func (b *JobStatusApplyConfiguration) WithReplicaStatuses(entries map[kubeflowv2beta1.MPIReplicaType]*kubeflowv2beta1.ReplicaStatus) *JobStatusApplyConfiguration {
	if b.ReplicaStatuses == nil && len(entries) > 0 {
		b.ReplicaStatuses = make(map[kubeflowv2beta1.MPIReplicaType]*kubeflowv2beta1.ReplicaStatus, len(entries))
	}
	for k, v := range entries {
		b.ReplicaStatuses[k] = v
	}
	return b
}

// WithStartTime sets the StartTime field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the StartTime field is set to the value of the last call.
func (b *JobStatusApplyConfiguration) WithStartTime(value v1.Time) *JobStatusApplyConfiguration {
	b.StartTime = &value
	return b
}

// WithCompletionTime sets the CompletionTime field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the CompletionTime field is set to the value of the last call.
func (b *JobStatusApplyConfiguration) WithCompletionTime(value v1.Time) *JobStatusApplyConfiguration {
	b.CompletionTime = &value
	return b
}

// WithLastReconcileTime sets the LastReconcileTime field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the LastReconcileTime field is set to the value of the last call.
func (b *JobStatusApplyConfiguration) WithLastReconcileTime(value v1.Time) *JobStatusApplyConfiguration {
	b.LastReconcileTime = &value
	return b
}
