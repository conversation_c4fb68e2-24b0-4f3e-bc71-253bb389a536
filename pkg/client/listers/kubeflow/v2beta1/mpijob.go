// Copyright 2025 The Kubeflow Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by lister-gen. DO NOT EDIT.

package v2beta1

import (
	v2beta1 "github.com/kubeflow/mpi-operator/pkg/apis/kubeflow/v2beta1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/listers"
	"k8s.io/client-go/tools/cache"
)

// MPIJobLister helps list MPIJobs.
// All objects returned here must be treated as read-only.
type MPIJobLister interface {
	// List lists all MPIJobs in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v2beta1.MPIJob, err error)
	// MPIJobs returns an object that can list and get MPIJobs.
	MPIJobs(namespace string) MPIJobNamespaceLister
	MPIJobListerExpansion
}

// mPIJobLister implements the MPIJobLister interface.
type mPIJobLister struct {
	listers.ResourceIndexer[*v2beta1.MPIJob]
}

// NewMPIJobLister returns a new MPIJobLister.
func NewMPIJobLister(indexer cache.Indexer) MPIJobLister {
	return &mPIJobLister{listers.New[*v2beta1.MPIJob](indexer, v2beta1.Resource("mpijob"))}
}

// MPIJobs returns an object that can list and get MPIJobs.
func (s *mPIJobLister) MPIJobs(namespace string) MPIJobNamespaceLister {
	return mPIJobNamespaceLister{listers.NewNamespaced[*v2beta1.MPIJob](s.ResourceIndexer, namespace)}
}

// MPIJobNamespaceLister helps list and get MPIJobs.
// All objects returned here must be treated as read-only.
type MPIJobNamespaceLister interface {
	// List lists all MPIJobs in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v2beta1.MPIJob, err error)
	// Get retrieves the MPIJob from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v2beta1.MPIJob, error)
	MPIJobNamespaceListerExpansion
}

// mPIJobNamespaceLister implements the MPIJobNamespaceLister
// interface.
type mPIJobNamespaceLister struct {
	listers.ResourceIndexer[*v2beta1.MPIJob]
}
