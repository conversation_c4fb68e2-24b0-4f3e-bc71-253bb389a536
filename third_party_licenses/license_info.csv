kubeflow/mpi-operator,https://github.com/kubeflow/mpi-operator/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubeflow/mpi-operator/master/LICENSE
sigma/systemstat,https://github.com/sigma/systemstat/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/sigma/systemstat/master/LICENSE
GoogleCloudPlatform/gcloud-golang,https://github.com/googleapis/google-cloud-go/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/googleapis/google-cloud-go/master/LICENSE
apache/thrift,https://github.com/apache/thrift/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/apache/thrift/master/LICENSE
Azure/azure-sdk-for-go,https://github.com/Azure/azure-sdk-for-go/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/Azure/azure-sdk-for-go/master/LICENSE
Azure/go-ansiterm,https://github.com/Azure/go-ansiterm/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/Azure/go-ansiterm/master/LICENSE
Azure/go-autorest,https://github.com/Azure/go-autorest/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/Azure/go-autorest/master/LICENSE
BurntSushi/toml,https://github.com/BurntSushi/toml/blob/master/COPYING,MIT License,https://raw.githubusercontent.com/BurntSushi/toml/master/COPYING
BurntSushi/xgb,https://github.com/BurntSushi/xgb/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/BurntSushi/xgb/master/LICENSE
GoogleCloudPlatform/k8s-cloud-provider,https://github.com/GoogleCloudPlatform/k8s-cloud-provider/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/GoogleCloudPlatform/k8s-cloud-provider/master/LICENSE
JeffAshton/win_pdh,https://github.com/JeffAshton/win_pdh/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/JeffAshton/win_pdh/master/LICENSE
MakeNowJust/heredoc,https://github.com/MakeNowJust/heredoc/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/MakeNowJust/heredoc/master/LICENSE
Microsoft/go-winio,https://github.com/microsoft/go-winio/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/microsoft/go-winio/master/LICENSE
Microsoft/hcsshim,https://github.com/microsoft/hcsshim/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/microsoft/hcsshim/master/LICENSE
NYTimes/gziphandler,https://github.com/nytimes/gziphandler/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/nytimes/gziphandler/master/LICENSE
PuerkitoBio/purell,https://github.com/PuerkitoBio/purell/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/PuerkitoBio/purell/master/LICENSE
PuerkitoBio/urlesc,https://github.com/PuerkitoBio/urlesc/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/PuerkitoBio/urlesc/master/LICENSE
Rican7/retry,https://github.com/Rican7/retry/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/Rican7/retry/master/LICENSE
alecthomas/template,https://github.com/alecthomas/template/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/alecthomas/template/master/LICENSE
alecthomas/units,https://github.com/alecthomas/units/blob/master/COPYING,MIT License,https://raw.githubusercontent.com/alecthomas/units/master/COPYING
anmitsu/go-shlex,https://github.com/anmitsu/go-shlex/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/anmitsu/go-shlex/master/LICENSE
armon/circbuf,https://github.com/armon/circbuf/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/armon/circbuf/master/LICENSE
armon/consul-api,https://github.com/armon/consul-api/blob/master/LICENSE,Mozilla Public License 2.0,https://raw.githubusercontent.com/armon/consul-api/master/LICENSE
asaskevich/govalidator,https://github.com/asaskevich/govalidator/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/asaskevich/govalidator/master/LICENSE
auth0/go-jwt-middleware,https://github.com/auth0/go-jwt-middleware/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/auth0/go-jwt-middleware/master/LICENSE
aws/aws-sdk-go,https://github.com/aws/aws-sdk-go/blob/master/LICENSE.txt,Apache License 2.0,https://raw.githubusercontent.com/aws/aws-sdk-go/master/LICENSE.txt
bazelbuild/bazel-gazelle,https://github.com/bazelbuild/bazel-gazelle/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/bazelbuild/bazel-gazelle/master/LICENSE
bazelbuild/buildtools,https://github.com/bazelbuild/buildtools/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/bazelbuild/buildtools/master/LICENSE
beorn7/perks,https://github.com/beorn7/perks/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/beorn7/perks/master/LICENSE
bifurcation/mint,https://github.com/bifurcation/mint/blob/master/LICENSE.md,MIT License,https://raw.githubusercontent.com/bifurcation/mint/master/LICENSE.md
blang/semver,https://github.com/blang/semver/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/blang/semver/master/LICENSE
boltdb/bolt,https://github.com/boltdb/bolt/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/boltdb/bolt/master/LICENSE
bradfitz/go-smtpd,https://github.com/bradfitz/go-smtpd/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/bradfitz/go-smtpd/master/LICENSE
caddyserver/caddy,https://github.com/caddyserver/caddy/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/caddyserver/caddy/master/LICENSE
cenkalti/backoff,https://github.com/cenkalti/backoff/blob/v4/LICENSE,MIT License,https://raw.githubusercontent.com/cenkalti/backoff/v4/LICENSE
cespare/prettybench,https://github.com/cespare/prettybench/blob/master/LICENSE.txt,MIT License,https://raw.githubusercontent.com/cespare/prettybench/master/LICENSE.txt
chai2010/gettext-go,https://github.com/chai2010/gettext-go/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/chai2010/gettext-go/master/LICENSE
checkpoint-restore/go-criu,https://github.com/checkpoint-restore/go-criu/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/checkpoint-restore/go-criu/master/LICENSE
cheekybits/genny,https://github.com/cheekybits/genny/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/cheekybits/genny/master/LICENSE
client9/misspell,https://github.com/client9/misspell/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/client9/misspell/master/LICENSE
cloudflare/cfssl,https://github.com/cloudflare/cfssl/blob/master/LICENSE,BSD 2-Clause "Simplified" License,https://raw.githubusercontent.com/cloudflare/cfssl/master/LICENSE
clusterhq/flocker-go,https://github.com/ClusterHQ/flocker-go/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/ClusterHQ/flocker-go/master/LICENSE
codedellemc/goscaleio,https://github.com/thecodeteam/goscaleio/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/thecodeteam/goscaleio/master/LICENSE
codegangsta/negroni,https://github.com/urfave/negroni/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/urfave/negroni/master/LICENSE
container-storage-interface/spec,https://github.com/container-storage-interface/spec/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/container-storage-interface/spec/master/LICENSE
containerd/console,https://github.com/containerd/console/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/containerd/console/master/LICENSE
containerd/containerd,https://github.com/containerd/containerd/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/containerd/containerd/master/LICENSE
containerd/typeurl,https://github.com/containerd/typeurl/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/containerd/typeurl/master/LICENSE
containernetworking/cni,https://github.com/containernetworking/cni/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/containernetworking/cni/master/LICENSE
coredns/corefile-migration,https://github.com/coredns/corefile-migration/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/coredns/corefile-migration/master/LICENSE
coreos/bbolt,https://github.com/etcd-io/bbolt/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/etcd-io/bbolt/master/LICENSE
coreos/etcd,https://github.com/etcd-io/etcd/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/etcd-io/etcd/master/LICENSE
coreos/go-etcd,https://github.com/coreos/go-etcd/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/coreos/go-etcd/master/LICENSE
coreos/go-oidc,https://github.com/coreos/go-oidc/blob/v2/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/coreos/go-oidc/v2/LICENSE
coreos/go-semver,https://github.com/coreos/go-semver/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/coreos/go-semver/master/LICENSE
coreos/go-systemd,https://github.com/coreos/go-systemd/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/coreos/go-systemd/master/LICENSE
coreos/pkg,https://github.com/coreos/pkg/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/coreos/pkg/master/LICENSE
coreos/rkt,https://github.com/rkt/rkt/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/rkt/rkt/master/LICENSE
cpuguy83/go-md2man,https://github.com/cpuguy83/go-md2man/blob/master/LICENSE.md,MIT License,https://raw.githubusercontent.com/cpuguy83/go-md2man/master/LICENSE.md
cyphar/filepath-securejoin,https://github.com/cyphar/filepath-securejoin/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/cyphar/filepath-securejoin/master/LICENSE
d2g/dhcp4,https://github.com/d2g/dhcp4/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/d2g/dhcp4/master/LICENSE
d2g/dhcp4client,https://github.com/d2g/dhcp4client/blob/v1/LICENSE,Mozilla Public License 2.0,https://raw.githubusercontent.com/d2g/dhcp4client/v1/LICENSE
davecgh/go-spew,https://github.com/davecgh/go-spew/blob/master/LICENSE,ISC License,https://raw.githubusercontent.com/davecgh/go-spew/master/LICENSE
daviddengcn/go-colortext,https://github.com/daviddengcn/go-colortext/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/daviddengcn/go-colortext/master/LICENSE
dgrijalva/jwt-go,https://github.com/dgrijalva/jwt-go/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/dgrijalva/jwt-go/master/LICENSE
dnaeon/go-vcr,https://github.com/dnaeon/go-vcr/blob/master/LICENSE,BSD 2-Clause License,https://raw.githubusercontent.com/dnaeon/go-vcr/master/LICENSE
docker/distribution,https://github.com/docker/distribution/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/docker/distribution/master/LICENSE
docker/docker,https://github.com/moby/moby/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/moby/moby/master/LICENSE
docker/go-connections,https://github.com/docker/go-connections/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/docker/go-connections/master/LICENSE
docker/go-units,https://github.com/docker/go-units/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/docker/go-units/master/LICENSE
docker/libnetwork,https://github.com/moby/libnetwork/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/moby/libnetwork/master/LICENSE
docker/spdystream,https://github.com/docker/spdystream/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/docker/spdystream/master/LICENSE
dustin/go-humanize,https://github.com/dustin/go-humanize/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/dustin/go-humanize/master/LICENSE
elazarl/goproxy,https://github.com/elazarl/goproxy/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/elazarl/goproxy/master/LICENSE
emicklei/go-restful,https://github.com/emicklei/go-restful/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/emicklei/go-restful/master/LICENSE
euank/go-kmsg-parser,https://github.com/euank/go-kmsg-parser/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/euank/go-kmsg-parser/master/LICENSE
evanphx/json-patch,https://github.com/evanphx/json-patch/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/evanphx/json-patch/master/LICENSE
exponent-io/jsonpath,https://github.com/exponent-io/jsonpath/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/exponent-io/jsonpath/master/LICENSE
fatih/camelcase,https://github.com/fatih/camelcase/blob/master/LICENSE.md,MIT License,https://raw.githubusercontent.com/fatih/camelcase/master/LICENSE.md
fatih/color,https://github.com/fatih/color/blob/master/LICENSE.md,MIT License,https://raw.githubusercontent.com/fatih/color/master/LICENSE.md
flynn/go-shlex,https://github.com/flynn-archive/go-shlex/blob/master/COPYING,Apache License 2.0,https://raw.githubusercontent.com/flynn-archive/go-shlex/master/COPYING
fsnotify/fsnotify,https://github.com/fsnotify/fsnotify/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/fsnotify/fsnotify/master/LICENSE
ghodss/yaml,https://github.com/ghodss/yaml/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/ghodss/yaml/master/LICENSE
gliderlabs/ssh,https://github.com/gliderlabs/ssh/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/gliderlabs/ssh/master/LICENSE
globalsign/mgo,https://github.com/globalsign/mgo/blob/master/LICENSE,BSD 2-Clause License,https://raw.githubusercontent.com/globalsign/mgo/master/LICENSE
go-acme/lego,https://github.com/go-acme/lego/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/go-acme/lego/master/LICENSE
go-bindata/go-bindata,https://github.com/go-bindata/go-bindata/blob/master/LICENSE,CC0 1.0 Universal,https://raw.githubusercontent.com/go-bindata/go-bindata/master/LICENSE
go-kit/kit,https://github.com/go-kit/kit/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/go-kit/kit/master/LICENSE
go-logfmt/logfmt,https://github.com/go-logfmt/logfmt/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/go-logfmt/logfmt/master/LICENSE
go-logr/logr,https://github.com/go-logr/logr/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/go-logr/logr/master/LICENSE
go-openapi/analysis,https://github.com/go-openapi/analysis/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/go-openapi/analysis/master/LICENSE
go-openapi/errors,https://github.com/go-openapi/errors/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/go-openapi/errors/master/LICENSE
go-openapi/jsonpointer,https://github.com/go-openapi/jsonpointer/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/go-openapi/jsonpointer/master/LICENSE
go-openapi/jsonreference,https://github.com/go-openapi/jsonreference/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/go-openapi/jsonreference/master/LICENSE
go-openapi/loads,https://github.com/go-openapi/loads/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/go-openapi/loads/master/LICENSE
go-openapi/runtime,https://github.com/go-openapi/runtime/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/go-openapi/runtime/master/LICENSE
go-openapi/spec,https://github.com/go-openapi/spec/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/go-openapi/spec/master/LICENSE
go-openapi/strfmt,https://github.com/go-openapi/strfmt/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/go-openapi/strfmt/master/LICENSE
go-openapi/swag,https://github.com/go-openapi/swag/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/go-openapi/swag/master/LICENSE
go-openapi/validate,https://github.com/go-openapi/validate/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/go-openapi/validate/master/LICENSE
go-ozzo/ozzo-validation,https://github.com/go-ozzo/ozzo-validation/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/go-ozzo/ozzo-validation/master/LICENSE
go-stack/stack,https://github.com/go-stack/stack/blob/master/LICENSE.md,MIT License,https://raw.githubusercontent.com/go-stack/stack/master/LICENSE.md
godbus/dbus,https://github.com/godbus/dbus/blob/master/LICENSE,BSD 2-Clause "Simplified" License,https://raw.githubusercontent.com/godbus/dbus/master/LICENSE
gogo/protobuf,https://github.com/gogo/protobuf/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/gogo/protobuf/master/LICENSE
golang/glog,https://github.com/golang/glog/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/golang/glog/master/LICENSE
golang/groupcache,https://github.com/golang/groupcache/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/golang/groupcache/master/LICENSE
golang/lint,https://github.com/golang/lint/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/lint/master/LICENSE
golang/mock,https://github.com/golang/mock/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/golang/mock/master/LICENSE
golang/protobuf,https://github.com/golang/protobuf/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/protobuf/master/LICENSE
golangplus/bytes,https://github.com/golangplus/bytes/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golangplus/bytes/master/LICENSE
golangplus/fmt,https://github.com/golangplus/fmt/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golangplus/fmt/master/LICENSE
golangplus/testing,https://github.com/golangplus/testing/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golangplus/testing/master/LICENSE
google/btree,https://github.com/google/btree/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/google/btree/master/LICENSE
google/cadvisor,https://github.com/google/cadvisor/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/google/cadvisor/master/LICENSE
google/certificate-transparency-go,https://github.com/google/certificate-transparency-go/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/google/certificate-transparency-go/master/LICENSE
google/go-cmp,https://github.com/google/go-cmp/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/google/go-cmp/master/LICENSE
google/go-github,https://github.com/google/go-github/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/google/go-github/master/LICENSE
google/go-querystring,https://github.com/google/go-querystring/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/google/go-querystring/master/LICENSE
google/gofuzz,https://github.com/google/gofuzz/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/google/gofuzz/master/LICENSE
google/martian,https://github.com/google/martian/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/google/martian/master/LICENSE
google/pprof,https://github.com/google/pprof/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/google/pprof/master/LICENSE
google/renameio,https://github.com/google/renameio/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/google/renameio/master/LICENSE
google/shlex,https://github.com/google/shlex/blob/master/COPYING,Apache License 2.0,https://raw.githubusercontent.com/google/shlex/master/COPYING
google/uuid,https://github.com/google/uuid/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/google/uuid/master/LICENSE
googleapis/gax-go,https://github.com/googleapis/gax-go/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/googleapis/gax-go/master/LICENSE
googleapis/gnostic,https://github.com/googleapis/gnostic/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/googleapis/gnostic/master/LICENSE
gophercloud/gophercloud,https://github.com/gophercloud/gophercloud/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/gophercloud/gophercloud/master/LICENSE
gopherjs/gopherjs,https://github.com/gopherjs/gopherjs/blob/master/LICENSE,BSD 2-Clause "Simplified" License,https://raw.githubusercontent.com/gopherjs/gopherjs/master/LICENSE
gorilla/context,https://github.com/gorilla/context/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/gorilla/context/master/LICENSE
gorilla/mux,https://github.com/gorilla/mux/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/gorilla/mux/master/LICENSE
gorilla/websocket,https://github.com/gorilla/websocket/blob/master/LICENSE,BSD 2-Clause "Simplified" License,https://raw.githubusercontent.com/gorilla/websocket/master/LICENSE
gotestyourself/gotestyourself,https://github.com/gotestyourself/gotest.tools/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/gotestyourself/gotest.tools/master/LICENSE
gregjones/httpcache,https://github.com/gregjones/httpcache/blob/master/LICENSE.txt,MIT License,https://raw.githubusercontent.com/gregjones/httpcache/master/LICENSE.txt
grpc-ecosystem/go-grpc-middleware,https://github.com/grpc-ecosystem/go-grpc-middleware/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/grpc-ecosystem/go-grpc-middleware/master/LICENSE
grpc-ecosystem/go-grpc-prometheus,https://github.com/grpc-ecosystem/go-grpc-prometheus/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/grpc-ecosystem/go-grpc-prometheus/master/LICENSE
grpc-ecosystem/grpc-gateway,https://github.com/grpc-ecosystem/grpc-gateway/blob/master/LICENSE.txt,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/grpc-ecosystem/grpc-gateway/master/LICENSE.txt
hashicorp/errwrap,https://github.com/hashicorp/errwrap/blob/master/LICENSE,Mozilla Public License 2.0,https://raw.githubusercontent.com/hashicorp/errwrap/master/LICENSE
hashicorp/go-multierror,https://github.com/hashicorp/go-multierror/blob/master/LICENSE,Mozilla Public License 2.0,https://raw.githubusercontent.com/hashicorp/go-multierror/master/LICENSE
hashicorp/go-syslog,https://github.com/hashicorp/go-syslog/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/hashicorp/go-syslog/master/LICENSE
hashicorp/golang-lru,https://github.com/hashicorp/golang-lru/blob/master/LICENSE,Mozilla Public License 2.0,https://raw.githubusercontent.com/hashicorp/golang-lru/master/LICENSE
hashicorp/hcl,https://github.com/hashicorp/hcl/blob/master/LICENSE,Mozilla Public License 2.0,https://raw.githubusercontent.com/hashicorp/hcl/master/LICENSE
heketi/heketi,https://github.com/heketi/heketi/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/heketi/heketi/master/LICENSE
heketi/rest,https://github.com/heketi/rest/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/heketi/rest/master/LICENSE
heketi/tests,https://github.com/heketi/tests/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/heketi/tests/master/LICENSE
hpcloud/tail,https://github.com/hpcloud/tail/blob/master/LICENSE.txt,MIT License,https://raw.githubusercontent.com/hpcloud/tail/master/LICENSE.txt
imdario/mergo,https://github.com/imdario/mergo/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/imdario/mergo/master/LICENSE
inconshreveable/mousetrap,https://github.com/inconshreveable/mousetrap/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/inconshreveable/mousetrap/master/LICENSE
jellevandenhooff/dkim,https://github.com/jellevandenhooff/dkim/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/jellevandenhooff/dkim/master/LICENSE
jimstudt/http-authentication,https://github.com/jimstudt/http-authentication/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/jimstudt/http-authentication/master/LICENSE
jmespath/go-jmespath,https://github.com/jmespath/go-jmespath/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/jmespath/go-jmespath/master/LICENSE
jonboulle/clockwork,https://github.com/jonboulle/clockwork/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/jonboulle/clockwork/master/LICENSE
json-iterator/go,https://github.com/json-iterator/go/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/json-iterator/go/master/LICENSE
jstemmer/go-junit-report,https://github.com/jstemmer/go-junit-report/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/jstemmer/go-junit-report/master/LICENSE
jteeuwen/go-bindata,https://github.com/jteeuwen/go-bindata/blob/master/LICENSE,CC0 1.0 Universal,https://raw.githubusercontent.com/jteeuwen/go-bindata/master/LICENSE
jtolds/gls,https://github.com/jtolio/gls/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/jtolio/gls/master/LICENSE
julienschmidt/httprouter,https://github.com/julienschmidt/httprouter/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/julienschmidt/httprouter/master/LICENSE
kardianos/osext,https://github.com/kardianos/osext/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/kardianos/osext/master/LICENSE
karrick/godirwalk,https://github.com/karrick/godirwalk/blob/master/LICENSE,BSD 2-Clause "Simplified" License,https://raw.githubusercontent.com/karrick/godirwalk/master/LICENSE
kisielk/errcheck,https://github.com/kisielk/errcheck/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/kisielk/errcheck/master/LICENSE
kisielk/gotool,https://github.com/kisielk/gotool/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/kisielk/gotool/master/LICENSE
klauspost/cpuid,https://github.com/klauspost/cpuid/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/klauspost/cpuid/master/LICENSE
konsorten/go-windows-terminal-sequences,https://github.com/konsorten/go-windows-terminal-sequences/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/konsorten/go-windows-terminal-sequences/master/LICENSE
kr/fs,https://github.com/kr/fs/blob/main/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/kr/fs/main/LICENSE
kr/pretty,https://github.com/kr/pretty/blob/main/License,MIT License,https://raw.githubusercontent.com/kr/pretty/main/License
kr/pty,https://github.com/kr/pty/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/kr/pty/master/LICENSE
kr/text,https://github.com/kr/text/blob/main/License,MIT License,https://raw.githubusercontent.com/kr/text/main/License
kubeflow/common,https://github.com/kubeflow/common/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubeflow/common/master/LICENSE
kubernetes-sigs/kube-batch,https://github.com/kubernetes-sigs/kube-batch/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes-sigs/kube-batch/master/LICENSE
kylelemons/godebug,https://github.com/kylelemons/godebug/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kylelemons/godebug/master/LICENSE
libopenstorage/openstorage,https://github.com/libopenstorage/openstorage/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/libopenstorage/openstorage/master/LICENSE
liggitt/tabwriter,https://github.com/liggitt/tabwriter/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/liggitt/tabwriter/master/LICENSE
lithammer/dedent,https://github.com/lithammer/dedent/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/lithammer/dedent/master/LICENSE
lpabon/godbc,https://github.com/lpabon/godbc/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/lpabon/godbc/master/LICENSE
lucas-clemente/aes12,https://github.com/lucas-clemente/aes12/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/lucas-clemente/aes12/master/LICENSE
lucas-clemente/quic-go,https://github.com/lucas-clemente/quic-go/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/lucas-clemente/quic-go/master/LICENSE
lucas-clemente/quic-go-certificates,https://github.com/lucas-clemente/quic-go-certificates/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/lucas-clemente/quic-go-certificates/master/LICENSE
magiconair/properties,https://github.com/magiconair/properties/blob/master/LICENSE,BSD 2-Clause License,https://raw.githubusercontent.com/magiconair/properties/master/LICENSE
mailru/easyjson,https://github.com/mailru/easyjson/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/mailru/easyjson/master/LICENSE
marstr/guid,https://github.com/marstr/guid/blob/master/LICENSE.txt,MIT License,https://raw.githubusercontent.com/marstr/guid/master/LICENSE.txt
marten-seemann/qtls,https://github.com/marten-seemann/qtls/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/marten-seemann/qtls/master/LICENSE
mattn/go-colorable,https://github.com/mattn/go-colorable/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/mattn/go-colorable/master/LICENSE
mattn/go-isatty,https://github.com/mattn/go-isatty/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/mattn/go-isatty/master/LICENSE
mattn/go-shellwords,https://github.com/mattn/go-shellwords/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/mattn/go-shellwords/master/LICENSE
matttproud/golang_protobuf_extensions,https://github.com/matttproud/golang_protobuf_extensions/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/matttproud/golang_protobuf_extensions/master/LICENSE
mesos/mesos-go,https://github.com/mesos/mesos-go/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/mesos/mesos-go/master/LICENSE
mholt/caddy,https://github.com/caddyserver/caddy/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/caddyserver/caddy/master/LICENSE
mholt/certmagic,https://github.com/caddyserver/certmagic/blob/master/LICENSE.txt,Apache License 2.0,https://raw.githubusercontent.com/caddyserver/certmagic/master/LICENSE.txt
microcosm-cc/bluemonday,https://github.com/microcosm-cc/bluemonday/blob/master/LICENSE.md,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/microcosm-cc/bluemonday/master/LICENSE.md
miekg/dns,https://github.com/miekg/dns/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/miekg/dns/master/LICENSE
mindprince/gonvml,https://github.com/mindprince/gonvml/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/mindprince/gonvml/master/LICENSE
mistifyio/go-zfs,https://github.com/mistifyio/go-zfs/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/mistifyio/go-zfs/master/LICENSE
mitchellh/go-homedir,https://github.com/mitchellh/go-homedir/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/mitchellh/go-homedir/master/LICENSE
mitchellh/go-wordwrap,https://github.com/mitchellh/go-wordwrap/blob/master/LICENSE.md,MIT License,https://raw.githubusercontent.com/mitchellh/go-wordwrap/master/LICENSE.md
mitchellh/mapstructure,https://github.com/mitchellh/mapstructure/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/mitchellh/mapstructure/master/LICENSE
modern-go/concurrent,https://github.com/modern-go/concurrent/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/modern-go/concurrent/master/LICENSE
modern-go/reflect2,https://github.com/modern-go/reflect2/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/modern-go/reflect2/master/LICENSE
mohae/deepcopy,https://github.com/mohae/deepcopy/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/mohae/deepcopy/master/LICENSE
morikuni/aec,https://github.com/morikuni/aec/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/morikuni/aec/master/LICENSE
mrunalp/fileutils,https://github.com/mrunalp/fileutils/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/mrunalp/fileutils/master/LICENSE
munnerz/goautoneg,https://github.com/munnerz/goautoneg/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/munnerz/goautoneg/master/LICENSE
mvdan/xurls,https://github.com/mvdan/xurls/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/mvdan/xurls/master/LICENSE
mwitkow/go-conntrack,https://github.com/mwitkow/go-conntrack/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/mwitkow/go-conntrack/master/LICENSE
mxk/go-flowrate,https://github.com/mxk/go-flowrate/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/mxk/go-flowrate/master/LICENSE
naoina/go-stringutil,https://github.com/naoina/go-stringutil/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/naoina/go-stringutil/master/LICENSE
naoina/toml,https://github.com/naoina/toml/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/naoina/toml/master/LICENSE
natefinch/lumberjack,https://github.com/natefinch/lumberjack/blob/v2.0/LICENSE,MIT License,https://raw.githubusercontent.com/natefinch/lumberjack/v2.0/LICENSE
neelance/astrewrite,https://github.com/neelance/astrewrite/blob/master/LICENSE,BSD 2-Clause "Simplified" License,https://raw.githubusercontent.com/neelance/astrewrite/master/LICENSE
neelance/sourcemap,https://github.com/neelance/sourcemap/blob/master/LICENSE,BSD 2-Clause "Simplified" License,https://raw.githubusercontent.com/neelance/sourcemap/master/LICENSE
onsi/ginkgo,https://github.com/onsi/ginkgo/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/onsi/ginkgo/master/LICENSE
onsi/gomega,https://github.com/onsi/gomega/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/onsi/gomega/master/LICENSE
opencontainers/go-digest,https://github.com/opencontainers/go-digest/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/opencontainers/go-digest/master/LICENSE
opencontainers/image-spec,https://github.com/opencontainers/image-spec/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/opencontainers/image-spec/master/LICENSE
opencontainers/runc,https://github.com/opencontainers/runc/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/opencontainers/runc/master/LICENSE
opencontainers/runtime-spec,https://github.com/opencontainers/runtime-spec/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/opencontainers/runtime-spec/master/LICENSE
opencontainers/selinux,https://github.com/opencontainers/selinux/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/opencontainers/selinux/master/LICENSE
openzipkin/zipkin-go,https://github.com/openzipkin/zipkin-go/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/openzipkin/zipkin-go/master/LICENSE
pborman/uuid,https://github.com/pborman/uuid/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/pborman/uuid/master/LICENSE
pelletier/go-toml,https://github.com/pelletier/go-toml/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/pelletier/go-toml/master/LICENSE
peterbourgon/diskv,https://github.com/peterbourgon/diskv/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/peterbourgon/diskv/master/LICENSE
pkg/errors,https://github.com/pkg/errors/blob/master/LICENSE,BSD 2-Clause "Simplified" License,https://raw.githubusercontent.com/pkg/errors/master/LICENSE
pkg/sftp,https://github.com/pkg/sftp/blob/master/LICENSE,BSD 2-Clause "Simplified" License,https://raw.githubusercontent.com/pkg/sftp/master/LICENSE
pmezard/go-difflib,https://github.com/pmezard/go-difflib/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/pmezard/go-difflib/master/LICENSE
pquerna/cachecontrol,https://github.com/pquerna/cachecontrol/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/pquerna/cachecontrol/master/LICENSE
pquerna/ffjson,https://github.com/pquerna/ffjson/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/pquerna/ffjson/master/LICENSE
prometheus/client_golang,https://github.com/prometheus/client_golang/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/prometheus/client_golang/master/LICENSE
prometheus/client_model,https://github.com/prometheus/client_model/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/prometheus/client_model/master/LICENSE
prometheus/common,https://github.com/prometheus/common/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/prometheus/common/master/LICENSE
prometheus/procfs,https://github.com/prometheus/procfs/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/prometheus/procfs/master/LICENSE
quobyte/api,https://github.com/quobyte/api/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/quobyte/api/master/LICENSE
remyoudompheng/bigfft,https://github.com/remyoudompheng/bigfft/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/remyoudompheng/bigfft/master/LICENSE
robfig/cron,https://github.com/robfig/cron/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/robfig/cron/master/LICENSE
rogpeppe/go-internal,https://github.com/rogpeppe/go-internal/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/rogpeppe/go-internal/master/LICENSE
rubiojr/go-vhd,https://github.com/rubiojr/go-vhd/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/rubiojr/go-vhd/master/LICENSE
russross/blackfriday,https://github.com/russross/blackfriday/blob/master/LICENSE.txt,BSD 2-Clause License,https://raw.githubusercontent.com/russross/blackfriday/master/LICENSE.txt
satori/go.uuid,https://github.com/satori/go.uuid/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/satori/go.uuid/master/LICENSE
seccomp/libseccomp-golang,https://github.com/seccomp/libseccomp-golang/blob/master/LICENSE,BSD 2-Clause "Simplified" License,https://raw.githubusercontent.com/seccomp/libseccomp-golang/master/LICENSE
sergi/go-diff,https://github.com/sergi/go-diff/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/sergi/go-diff/master/LICENSE
shurcooL/component,https://github.com/shurcooL/component/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/shurcooL/component/master/LICENSE
shurcooL/events,https://github.com/shurcooL/events/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/shurcooL/events/master/LICENSE
shurcooL/gofontwoff,https://github.com/shurcooL/gofontwoff/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/shurcooL/gofontwoff/master/LICENSE
shurcooL/gopherjslib,https://github.com/shurcooL/gopherjslib/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/shurcooL/gopherjslib/master/LICENSE
shurcooL/highlight_diff,https://github.com/shurcooL/highlight_diff/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/shurcooL/highlight_diff/master/LICENSE
shurcooL/home,https://github.com/shurcooL/home/<USER>/master/LICENSE,MIT License,https://raw.githubusercontent.com/shurcooL/home/<USER>/LICENSE
shurcooL/htmlg,https://github.com/shurcooL/htmlg/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/shurcooL/htmlg/master/LICENSE
shurcooL/httpfs,https://github.com/shurcooL/httpfs/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/shurcooL/httpfs/master/LICENSE
shurcooL/httpgzip,https://github.com/shurcooL/httpgzip/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/shurcooL/httpgzip/master/LICENSE
shurcooL/issuesapp,https://github.com/shurcooL/issuesapp/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/shurcooL/issuesapp/master/LICENSE
shurcooL/octicon,https://github.com/shurcooL/octicon/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/shurcooL/octicon/master/LICENSE
shurcooL/reactions,https://github.com/shurcooL/reactions/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/shurcooL/reactions/master/LICENSE
shurcooL/sanitized_anchor_name,https://github.com/shurcooL/sanitized_anchor_name/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/shurcooL/sanitized_anchor_name/master/LICENSE
shurcooL/webdavfs,https://github.com/shurcooL/webdavfs/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/shurcooL/webdavfs/master/LICENSE
sigma/go-inotify,https://github.com/sigma/go-inotify/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/sigma/go-inotify/master/LICENSE
sirupsen/logrus,https://github.com/sirupsen/logrus/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/sirupsen/logrus/master/LICENSE
smartystreets/assertions,https://github.com/smartystreets/assertions/blob/master/LICENSE.md,MIT License,https://raw.githubusercontent.com/smartystreets/assertions/master/LICENSE.md
smartystreets/goconvey,https://github.com/smartystreets/goconvey/blob/master/LICENSE.md,MIT License,https://raw.githubusercontent.com/smartystreets/goconvey/master/LICENSE.md
soheilhy/cmux,https://github.com/soheilhy/cmux/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/soheilhy/cmux/master/LICENSE
sourcegraph/annotate,https://github.com/sourcegraph/annotate/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/sourcegraph/annotate/master/LICENSE
sourcegraph/syntaxhighlight,https://github.com/sourcegraph/syntaxhighlight/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/sourcegraph/syntaxhighlight/master/LICENSE
spf13/afero,https://github.com/spf13/afero/blob/master/LICENSE.txt,Apache License 2.0,https://raw.githubusercontent.com/spf13/afero/master/LICENSE.txt
spf13/cast,https://github.com/spf13/cast/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/spf13/cast/master/LICENSE
spf13/cobra,https://github.com/spf13/cobra/blob/master/LICENSE.txt,Apache License 2.0,https://raw.githubusercontent.com/spf13/cobra/master/LICENSE.txt
spf13/jwalterweatherman,https://github.com/spf13/jwalterweatherman/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/spf13/jwalterweatherman/master/LICENSE
spf13/pflag,https://github.com/spf13/pflag/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/spf13/pflag/master/LICENSE
spf13/viper,https://github.com/spf13/viper/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/spf13/viper/master/LICENSE
storageos/go-api,https://github.com/storageos/go-api/blob/master/LICENCE,MIT License,https://raw.githubusercontent.com/storageos/go-api/master/LICENCE
stretchr/objx,https://github.com/stretchr/objx/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/stretchr/objx/master/LICENSE
stretchr/testify,https://github.com/stretchr/testify/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/stretchr/testify/master/LICENSE
syndtr/gocapability,https://github.com/syndtr/gocapability/blob/master/LICENSE,BSD 2-Clause "Simplified" License,https://raw.githubusercontent.com/syndtr/gocapability/master/LICENSE
tarm/serial,https://github.com/tarm/serial/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/tarm/serial/master/LICENSE
thecodeteam/goscaleio,https://github.com/thecodeteam/goscaleio/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/thecodeteam/goscaleio/master/LICENSE
tmc/grpc-websocket-proxy,https://github.com/tmc/grpc-websocket-proxy/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/tmc/grpc-websocket-proxy/master/LICENSE
ugorji/go,https://github.com/ugorji/go/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/ugorji/go/master/LICENSE
urfave/negroni,https://github.com/urfave/negroni/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/urfave/negroni/master/LICENSE
vishvananda/netlink,https://github.com/vishvananda/netlink/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/vishvananda/netlink/master/LICENSE
vishvananda/netns,https://github.com/vishvananda/netns/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/vishvananda/netns/master/LICENSE
vmware/govmomi,https://github.com/vmware/govmomi/blob/master/LICENSE.txt,Apache License 2.0,https://raw.githubusercontent.com/vmware/govmomi/master/LICENSE.txt
vmware/photon-controller-go-sdk,https://github.com/vmware-archive/photon-controller-go-sdk/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/vmware-archive/photon-controller-go-sdk/master/LICENSE
xanzy/go-cloudstack,https://github.com/xanzy/go-cloudstack/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/xanzy/go-cloudstack/master/LICENSE
xiang90/probing,https://github.com/xiang90/probing/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/xiang90/probing/master/LICENSE
xlab/handysort,https://github.com/xlab/handysort/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/xlab/handysort/master/LICENSE
xordataexchange/crypt,https://github.com/xordataexchange/crypt/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/xordataexchange/crypt/master/LICENSE
etcd-io/bbolt,https://github.com/etcd-io/bbolt/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/etcd-io/bbolt/master/LICENSE
census-instrumentation/opencensus-go,https://github.com/census-instrumentation/opencensus-go/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/census-instrumentation/opencensus-go/master/LICENSE
uber-go/atomic,https://github.com/uber-go/atomic/blob/master/LICENSE.txt,MIT License,https://raw.githubusercontent.com/uber-go/atomic/master/LICENSE.txt
uber-go/multierr,https://github.com/uber-go/multierr/blob/master/LICENSE.txt,MIT License,https://raw.githubusercontent.com/uber-go/multierr/master/LICENSE.txt
uber-go/zap,https://github.com/uber-go/zap/blob/master/LICENSE.txt,MIT License,https://raw.githubusercontent.com/uber-go/zap/master/LICENSE.txt
go4org/go4,https://github.com/go4org/go4/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/go4org/go4/master/LICENSE
golang/build,https://github.com/golang/build/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/build/master/LICENSE
golang/crypto,https://github.com/golang/crypto/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/crypto/master/LICENSE
golang/exp,https://github.com/golang/exp/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/exp/master/LICENSE
golang/image,https://github.com/golang/image/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/image/master/LICENSE
golang/mobile,https://github.com/golang/mobile/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/mobile/master/LICENSE
golang/mod,https://github.com/golang/mod/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/mod/master/LICENSE
golang/net,https://github.com/golang/net/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/net/master/LICENSE
golang/oauth2,https://github.com/golang/oauth2/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/oauth2/master/LICENSE
golang/perf,https://github.com/golang/perf/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/perf/master/LICENSE
golang/sync,https://github.com/golang/sync/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/sync/master/LICENSE
golang/sys,https://github.com/golang/sys/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/sys/master/LICENSE
golang/text,https://github.com/golang/text/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/text/master/LICENSE
golang/time,https://github.com/golang/time/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/time/master/LICENSE
golang/tools,https://github.com/golang/tools/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/golang/tools/master/LICENSE
gonum/gonum,https://github.com/gonum/gonum/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/gonum/gonum/master/LICENSE
gonum/netlib,https://github.com/gonum/netlib/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/gonum/netlib/master/LICENSE
google/google-api-go-client,https://github.com/googleapis/google-api-go-client/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/googleapis/google-api-go-client/master/LICENSE
golang/appengine,https://github.com/golang/appengine/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/golang/appengine/master/LICENSE
google/go-genproto,https://github.com/googleapis/go-genproto/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/googleapis/go-genproto/master/LICENSE
grpc/grpc-go,https://github.com/grpc/grpc-go/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/grpc/grpc-go/master/LICENSE
airbrake/gobrake,https://github.com/airbrake/gobrake/blob/master/LICENSE.md,MIT License,https://raw.githubusercontent.com/airbrake/gobrake/master/LICENSE.md
alecthomas/kingpin,https://github.com/alecthomas/kingpin/blob/master/COPYING,MIT License,https://raw.githubusercontent.com/alecthomas/kingpin/master/COPYING
go-check/check,https://github.com/go-check/check/blob/v1/LICENSE,BSD 2-Clause License,https://raw.githubusercontent.com/go-check/check/v1/LICENSE
go-errgo/errgo,https://github.com/go-errgo/errgo/blob/v1/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/go-errgo/errgo/v1/LICENSE
go-gcfg/gcfg,https://github.com/go-gcfg/gcfg/blob/v1/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/go-gcfg/gcfg/v1/LICENSE
gemnasium/logrus-airbrake-hook,https://github.com/gemnasium/logrus-airbrake-hook/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/gemnasium/logrus-airbrake-hook/master/LICENSE
go-inf/inf,https://github.com/go-inf/inf/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/go-inf/inf/master/LICENSE
mcuadros/go-syslog,https://github.com/mcuadros/go-syslog/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/mcuadros/go-syslog/master/LICENSE
square/go-jose,https://github.com/square/go-jose/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/square/go-jose/master/LICENSE
go-tomb/tomb,https://github.com/go-tomb/tomb/blob/v1/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/go-tomb/tomb/v1/LICENSE
go-warnings/warnings,https://github.com/go-warnings/warnings/blob/master/LICENSE,BSD 2-Clause "Simplified" License,https://raw.githubusercontent.com/go-warnings/warnings/master/LICENSE
go-yaml/yaml,https://github.com/go-yaml/yaml/blob/v2/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/go-yaml/yaml/v2/LICENSE
gotestyourself/gotest.tools,https://github.com/gotestyourself/gotest.tools/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/gotestyourself/gotest.tools/master/LICENSE
gotestyourself/gotestsum,https://github.com/gotestyourself/gotestsum/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/gotestyourself/gotestsum/master/LICENSE
go4org/grpc,https://github.com/go4org/grpc/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/go4org/grpc/master/LICENSE
dominikh/go-tools,https://github.com/dominikh/go-tools/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/dominikh/go-tools/master/LICENSE
kubernetes/api,https://github.com/kubernetes/api/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/api/master/LICENSE
kubernetes/apiextensions-apiserver,https://github.com/kubernetes/apiextensions-apiserver/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/apiextensions-apiserver/master/LICENSE
kubernetes/apimachinery,https://github.com/kubernetes/apimachinery/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/apimachinery/master/LICENSE
kubernetes/apiserver,https://github.com/kubernetes/apiserver/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/apiserver/master/LICENSE
kubernetes/cli-runtime,https://github.com/kubernetes/cli-runtime/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/cli-runtime/master/LICENSE
kubernetes/client-go,https://github.com/kubernetes/client-go/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/client-go/master/LICENSE
kubernetes/cloud-provider,https://github.com/kubernetes/cloud-provider/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/cloud-provider/master/LICENSE
kubernetes/cluster-bootstrap,https://github.com/kubernetes/cluster-bootstrap/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/cluster-bootstrap/master/LICENSE
kubernetes/code-generator,https://github.com/kubernetes/code-generator/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/code-generator/master/LICENSE
kubernetes/component-base,https://github.com/kubernetes/component-base/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/component-base/master/LICENSE
kubernetes/cri-api,https://github.com/kubernetes/cri-api/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/cri-api/master/LICENSE
kubernetes/csi-api,https://github.com/kubernetes/csi-api/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/csi-api/master/LICENSE
kubernetes/csi-translation-lib,https://github.com/kubernetes/csi-translation-lib/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/csi-translation-lib/master/LICENSE
kubernetes/gengo,https://github.com/kubernetes/gengo/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/gengo/master/LICENSE
kubernetes/heapster,https://github.com/kubernetes-retired/heapster/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes-retired/heapster/master/LICENSE
kubernetes/klog,https://github.com/kubernetes/klog/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/klog/master/LICENSE
kubernetes/kube-aggregator,https://github.com/kubernetes/kube-aggregator/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/kube-aggregator/master/LICENSE
kubernetes/kube-controller-manager,https://github.com/kubernetes/kube-controller-manager/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/kube-controller-manager/master/LICENSE
kubernetes/kube-openapi,https://github.com/kubernetes/kube-openapi/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/kube-openapi/master/LICENSE
kubernetes/kube-proxy,https://github.com/kubernetes/kube-proxy/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/kube-proxy/master/LICENSE
kubernetes/kube-scheduler,https://github.com/kubernetes/kube-scheduler/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/kube-scheduler/master/LICENSE
kubernetes/kubectl,https://github.com/kubernetes/kubectl/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/kubectl/master/LICENSE
kubernetes/kubelet,https://github.com/kubernetes/kubelet/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/kubelet/master/LICENSE
kubernetes/kubernetes,https://github.com/kubernetes/kubernetes/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/kubernetes/master/LICENSE
kubernetes/legacy-cloud-providers,https://github.com/kubernetes/legacy-cloud-providers/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/legacy-cloud-providers/master/LICENSE
kubernetes/metrics,https://github.com/kubernetes/metrics/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/metrics/master/LICENSE
kubernetes/repo-infra,https://github.com/kubernetes/repo-infra/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/repo-infra/master/LICENSE
kubernetes/sample-apiserver,https://github.com/kubernetes/sample-apiserver/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/sample-apiserver/master/LICENSE
kubernetes/utils,https://github.com/kubernetes/utils/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes/utils/master/LICENSE
cznic/cc,https://github.com/cznic/cc/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/cznic/cc/master/LICENSE
cznic/golex,https://github.com/cznic/golex/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/cznic/golex/master/LICENSE
cznic/mathutil,https://github.com/cznic/mathutil/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/cznic/mathutil/master/LICENSE
cznic/strutil,https://github.com/cznic/strutil/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/cznic/strutil/master/LICENSE
cznic/xc,https://github.com/cznic/xc/blob/master/LICENSE,BSD 3-Clause "New" or "Revised" License,https://raw.githubusercontent.com/cznic/xc/master/LICENSE
kubernetes-sigs/kustomize,https://github.com/kubernetes-sigs/kustomize/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/LICENSE
kubernetes-sigs/structured-merge-diff,https://github.com/kubernetes-sigs/structured-merge-diff/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/kubernetes-sigs/structured-merge-diff/master/LICENSE
kubernetes-sigs/yaml,https://github.com/kubernetes-sigs/yaml/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/kubernetes-sigs/yaml/master/LICENSE
sourcegraph/go-diff,https://github.com/sourcegraph/go-diff/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/sourcegraph/go-diff/master/LICENSE
sqs/pbtypes,https://github.com/sqs/pbtypes/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/sqs/pbtypes/master/LICENSE
fvbommel/util,https://github.com/fvbommel/util/blob/master/LICENSE,MIT License,https://raw.githubusercontent.com/fvbommel/util/master/LICENSE
volcano-sh/volcano,https://github.com/volcano-sh/volcano/blob/master/LICENSE,Apache License 2.0,https://raw.githubusercontent.com/volcano-sh/volcano/master/LICENSE
