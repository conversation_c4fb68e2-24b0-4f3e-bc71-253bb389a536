# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


import inspect
import pprint
import re  # noqa: F401
import six

from mpijob.configuration import Configuration


class V1APIResourceList(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'api_version': 'str',
        'group_version': 'str',
        'kind': 'str',
        'resources': 'list[V1APIResource]'
    }

    attribute_map = {
        'api_version': 'apiVersion',
        'group_version': 'groupVersion',
        'kind': 'kind',
        'resources': 'resources'
    }

    def __init__(self, api_version=None, group_version='', kind=None, resources=None, local_vars_configuration=None):  # noqa: E501
        """V1APIResourceList - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration.get_default_copy()
        self.local_vars_configuration = local_vars_configuration

        self._api_version = None
        self._group_version = None
        self._kind = None
        self._resources = None
        self.discriminator = None

        if api_version is not None:
            self.api_version = api_version
        self.group_version = group_version
        if kind is not None:
            self.kind = kind
        self.resources = resources

    @property
    def api_version(self):
        """Gets the api_version of this V1APIResourceList.  # noqa: E501

        APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources  # noqa: E501

        :return: The api_version of this V1APIResourceList.  # noqa: E501
        :rtype: str
        """
        return self._api_version

    @api_version.setter
    def api_version(self, api_version):
        """Sets the api_version of this V1APIResourceList.

        APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources  # noqa: E501

        :param api_version: The api_version of this V1APIResourceList.  # noqa: E501
        :type api_version: str
        """

        self._api_version = api_version

    @property
    def group_version(self):
        """Gets the group_version of this V1APIResourceList.  # noqa: E501

        groupVersion is the group and version this APIResourceList is for.  # noqa: E501

        :return: The group_version of this V1APIResourceList.  # noqa: E501
        :rtype: str
        """
        return self._group_version

    @group_version.setter
    def group_version(self, group_version):
        """Sets the group_version of this V1APIResourceList.

        groupVersion is the group and version this APIResourceList is for.  # noqa: E501

        :param group_version: The group_version of this V1APIResourceList.  # noqa: E501
        :type group_version: str
        """
        if self.local_vars_configuration.client_side_validation and group_version is None:  # noqa: E501
            raise ValueError("Invalid value for `group_version`, must not be `None`")  # noqa: E501

        self._group_version = group_version

    @property
    def kind(self):
        """Gets the kind of this V1APIResourceList.  # noqa: E501

        Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds  # noqa: E501

        :return: The kind of this V1APIResourceList.  # noqa: E501
        :rtype: str
        """
        return self._kind

    @kind.setter
    def kind(self, kind):
        """Sets the kind of this V1APIResourceList.

        Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds  # noqa: E501

        :param kind: The kind of this V1APIResourceList.  # noqa: E501
        :type kind: str
        """

        self._kind = kind

    @property
    def resources(self):
        """Gets the resources of this V1APIResourceList.  # noqa: E501

        resources contains the name of the resources and if they are namespaced.  # noqa: E501

        :return: The resources of this V1APIResourceList.  # noqa: E501
        :rtype: list[V1APIResource]
        """
        return self._resources

    @resources.setter
    def resources(self, resources):
        """Sets the resources of this V1APIResourceList.

        resources contains the name of the resources and if they are namespaced.  # noqa: E501

        :param resources: The resources of this V1APIResourceList.  # noqa: E501
        :type resources: list[V1APIResource]
        """
        if self.local_vars_configuration.client_side_validation and resources is None:  # noqa: E501
            raise ValueError("Invalid value for `resources`, must not be `None`")  # noqa: E501

        self._resources = resources

    def to_dict(self, serialize=False):
        """Returns the model properties as a dict"""
        result = {}

        def convert(x):
            if hasattr(x, "to_dict"):
                args = inspect.getargspec(x.to_dict).args
                if len(args) == 1:
                    return x.to_dict()
                else:
                    return x.to_dict(serialize)
            else:
                return x

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            attr = self.attribute_map.get(attr, attr) if serialize else attr
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: convert(x),
                    value
                ))
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], convert(item[1])),
                    value.items()
                ))
            else:
                result[attr] = convert(value)

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1APIResourceList):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1APIResourceList):
            return True

        return self.to_dict() != other.to_dict()
