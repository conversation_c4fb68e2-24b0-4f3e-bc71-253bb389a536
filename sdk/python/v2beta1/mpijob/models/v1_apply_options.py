# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


import inspect
import pprint
import re  # noqa: F401
import six

from mpijob.configuration import Configuration


class V1ApplyOptions(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'api_version': 'str',
        'dry_run': 'list[str]',
        'field_manager': 'str',
        'force': 'bool',
        'kind': 'str'
    }

    attribute_map = {
        'api_version': 'apiVersion',
        'dry_run': 'dryRun',
        'field_manager': 'fieldManager',
        'force': 'force',
        'kind': 'kind'
    }

    def __init__(self, api_version=None, dry_run=None, field_manager='', force=False, kind=None, local_vars_configuration=None):  # noqa: E501
        """V1ApplyOptions - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration.get_default_copy()
        self.local_vars_configuration = local_vars_configuration

        self._api_version = None
        self._dry_run = None
        self._field_manager = None
        self._force = None
        self._kind = None
        self.discriminator = None

        if api_version is not None:
            self.api_version = api_version
        if dry_run is not None:
            self.dry_run = dry_run
        self.field_manager = field_manager
        self.force = force
        if kind is not None:
            self.kind = kind

    @property
    def api_version(self):
        """Gets the api_version of this V1ApplyOptions.  # noqa: E501

        APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources  # noqa: E501

        :return: The api_version of this V1ApplyOptions.  # noqa: E501
        :rtype: str
        """
        return self._api_version

    @api_version.setter
    def api_version(self, api_version):
        """Sets the api_version of this V1ApplyOptions.

        APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources  # noqa: E501

        :param api_version: The api_version of this V1ApplyOptions.  # noqa: E501
        :type api_version: str
        """

        self._api_version = api_version

    @property
    def dry_run(self):
        """Gets the dry_run of this V1ApplyOptions.  # noqa: E501

        When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed  # noqa: E501

        :return: The dry_run of this V1ApplyOptions.  # noqa: E501
        :rtype: list[str]
        """
        return self._dry_run

    @dry_run.setter
    def dry_run(self, dry_run):
        """Sets the dry_run of this V1ApplyOptions.

        When present, indicates that modifications should not be persisted. An invalid or unrecognized dryRun directive will result in an error response and no further processing of the request. Valid values are: - All: all dry run stages will be processed  # noqa: E501

        :param dry_run: The dry_run of this V1ApplyOptions.  # noqa: E501
        :type dry_run: list[str]
        """

        self._dry_run = dry_run

    @property
    def field_manager(self):
        """Gets the field_manager of this V1ApplyOptions.  # noqa: E501

        fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint. This field is required.  # noqa: E501

        :return: The field_manager of this V1ApplyOptions.  # noqa: E501
        :rtype: str
        """
        return self._field_manager

    @field_manager.setter
    def field_manager(self, field_manager):
        """Sets the field_manager of this V1ApplyOptions.

        fieldManager is a name associated with the actor or entity that is making these changes. The value must be less than or 128 characters long, and only contain printable characters, as defined by https://golang.org/pkg/unicode/#IsPrint. This field is required.  # noqa: E501

        :param field_manager: The field_manager of this V1ApplyOptions.  # noqa: E501
        :type field_manager: str
        """
        if self.local_vars_configuration.client_side_validation and field_manager is None:  # noqa: E501
            raise ValueError("Invalid value for `field_manager`, must not be `None`")  # noqa: E501

        self._field_manager = field_manager

    @property
    def force(self):
        """Gets the force of this V1ApplyOptions.  # noqa: E501

        Force is going to \"force\" Apply requests. It means user will re-acquire conflicting fields owned by other people.  # noqa: E501

        :return: The force of this V1ApplyOptions.  # noqa: E501
        :rtype: bool
        """
        return self._force

    @force.setter
    def force(self, force):
        """Sets the force of this V1ApplyOptions.

        Force is going to \"force\" Apply requests. It means user will re-acquire conflicting fields owned by other people.  # noqa: E501

        :param force: The force of this V1ApplyOptions.  # noqa: E501
        :type force: bool
        """
        if self.local_vars_configuration.client_side_validation and force is None:  # noqa: E501
            raise ValueError("Invalid value for `force`, must not be `None`")  # noqa: E501

        self._force = force

    @property
    def kind(self):
        """Gets the kind of this V1ApplyOptions.  # noqa: E501

        Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds  # noqa: E501

        :return: The kind of this V1ApplyOptions.  # noqa: E501
        :rtype: str
        """
        return self._kind

    @kind.setter
    def kind(self, kind):
        """Sets the kind of this V1ApplyOptions.

        Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds  # noqa: E501

        :param kind: The kind of this V1ApplyOptions.  # noqa: E501
        :type kind: str
        """

        self._kind = kind

    def to_dict(self, serialize=False):
        """Returns the model properties as a dict"""
        result = {}

        def convert(x):
            if hasattr(x, "to_dict"):
                args = inspect.getargspec(x.to_dict).args
                if len(args) == 1:
                    return x.to_dict()
                else:
                    return x.to_dict(serialize)
            else:
                return x

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            attr = self.attribute_map.get(attr, attr) if serialize else attr
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: convert(x),
                    value
                ))
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], convert(item[1])),
                    value.items()
                ))
            else:
                result[attr] = convert(value)

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1ApplyOptions):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1ApplyOptions):
            return True

        return self.to_dict() != other.to_dict()
