# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


import inspect
import pprint
import re  # noqa: F401
import six

from mpijob.configuration import Configuration


class V2beta1ReplicaStatus(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'active': 'int',
        'failed': 'int',
        'label_selector': 'V1LabelSelector',
        'selector': 'str',
        'succeeded': 'int'
    }

    attribute_map = {
        'active': 'active',
        'failed': 'failed',
        'label_selector': 'labelSelector',
        'selector': 'selector',
        'succeeded': 'succeeded'
    }

    def __init__(self, active=None, failed=None, label_selector=None, selector=None, succeeded=None, local_vars_configuration=None):  # noqa: E501
        """V2beta1ReplicaStatus - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration.get_default_copy()
        self.local_vars_configuration = local_vars_configuration

        self._active = None
        self._failed = None
        self._label_selector = None
        self._selector = None
        self._succeeded = None
        self.discriminator = None

        if active is not None:
            self.active = active
        if failed is not None:
            self.failed = failed
        if label_selector is not None:
            self.label_selector = label_selector
        if selector is not None:
            self.selector = selector
        if succeeded is not None:
            self.succeeded = succeeded

    @property
    def active(self):
        """Gets the active of this V2beta1ReplicaStatus.  # noqa: E501

        The number of actively running pods.  # noqa: E501

        :return: The active of this V2beta1ReplicaStatus.  # noqa: E501
        :rtype: int
        """
        return self._active

    @active.setter
    def active(self, active):
        """Sets the active of this V2beta1ReplicaStatus.

        The number of actively running pods.  # noqa: E501

        :param active: The active of this V2beta1ReplicaStatus.  # noqa: E501
        :type active: int
        """

        self._active = active

    @property
    def failed(self):
        """Gets the failed of this V2beta1ReplicaStatus.  # noqa: E501

        The number of pods which reached phase failed.  # noqa: E501

        :return: The failed of this V2beta1ReplicaStatus.  # noqa: E501
        :rtype: int
        """
        return self._failed

    @failed.setter
    def failed(self, failed):
        """Sets the failed of this V2beta1ReplicaStatus.

        The number of pods which reached phase failed.  # noqa: E501

        :param failed: The failed of this V2beta1ReplicaStatus.  # noqa: E501
        :type failed: int
        """

        self._failed = failed

    @property
    def label_selector(self):
        """Gets the label_selector of this V2beta1ReplicaStatus.  # noqa: E501


        :return: The label_selector of this V2beta1ReplicaStatus.  # noqa: E501
        :rtype: V1LabelSelector
        """
        return self._label_selector

    @label_selector.setter
    def label_selector(self, label_selector):
        """Sets the label_selector of this V2beta1ReplicaStatus.


        :param label_selector: The label_selector of this V2beta1ReplicaStatus.  # noqa: E501
        :type label_selector: V1LabelSelector
        """

        self._label_selector = label_selector

    @property
    def selector(self):
        """Gets the selector of this V2beta1ReplicaStatus.  # noqa: E501

        A selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty selector matches all objects. A null selector matches no objects.  # noqa: E501

        :return: The selector of this V2beta1ReplicaStatus.  # noqa: E501
        :rtype: str
        """
        return self._selector

    @selector.setter
    def selector(self, selector):
        """Sets the selector of this V2beta1ReplicaStatus.

        A selector is a label query over a set of resources. The result of matchLabels and matchExpressions are ANDed. An empty selector matches all objects. A null selector matches no objects.  # noqa: E501

        :param selector: The selector of this V2beta1ReplicaStatus.  # noqa: E501
        :type selector: str
        """

        self._selector = selector

    @property
    def succeeded(self):
        """Gets the succeeded of this V2beta1ReplicaStatus.  # noqa: E501

        The number of pods which reached phase succeeded.  # noqa: E501

        :return: The succeeded of this V2beta1ReplicaStatus.  # noqa: E501
        :rtype: int
        """
        return self._succeeded

    @succeeded.setter
    def succeeded(self, succeeded):
        """Sets the succeeded of this V2beta1ReplicaStatus.

        The number of pods which reached phase succeeded.  # noqa: E501

        :param succeeded: The succeeded of this V2beta1ReplicaStatus.  # noqa: E501
        :type succeeded: int
        """

        self._succeeded = succeeded

    def to_dict(self, serialize=False):
        """Returns the model properties as a dict"""
        result = {}

        def convert(x):
            if hasattr(x, "to_dict"):
                args = inspect.getargspec(x.to_dict).args
                if len(args) == 1:
                    return x.to_dict()
                else:
                    return x.to_dict(serialize)
            else:
                return x

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            attr = self.attribute_map.get(attr, attr) if serialize else attr
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: convert(x),
                    value
                ))
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], convert(item[1])),
                    value.items()
                ))
            else:
                result[attr] = convert(value)

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V2beta1ReplicaStatus):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V2beta1ReplicaStatus):
            return True

        return self.to_dict() != other.to_dict()
