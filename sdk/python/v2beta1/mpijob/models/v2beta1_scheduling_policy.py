# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


import inspect
import pprint
import re  # noqa: F401
import six

from mpijob.configuration import Configuration


class V2beta1SchedulingPolicy(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'min_available': 'int',
        'min_resources': 'dict(str, ResourceQuantity)',
        'priority_class': 'str',
        'queue': 'str',
        'schedule_timeout_seconds': 'int'
    }

    attribute_map = {
        'min_available': 'minAvailable',
        'min_resources': 'minResources',
        'priority_class': 'priorityClass',
        'queue': 'queue',
        'schedule_timeout_seconds': 'scheduleTimeoutSeconds'
    }

    def __init__(self, min_available=None, min_resources=None, priority_class=None, queue=None, schedule_timeout_seconds=None, local_vars_configuration=None):  # noqa: E501
        """V2beta1SchedulingPolicy - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration.get_default_copy()
        self.local_vars_configuration = local_vars_configuration

        self._min_available = None
        self._min_resources = None
        self._priority_class = None
        self._queue = None
        self._schedule_timeout_seconds = None
        self.discriminator = None

        if min_available is not None:
            self.min_available = min_available
        if min_resources is not None:
            self.min_resources = min_resources
        if priority_class is not None:
            self.priority_class = priority_class
        if queue is not None:
            self.queue = queue
        if schedule_timeout_seconds is not None:
            self.schedule_timeout_seconds = schedule_timeout_seconds

    @property
    def min_available(self):
        """Gets the min_available of this V2beta1SchedulingPolicy.  # noqa: E501

        MinAvailable defines the minimal number of member to run the PodGroup. If the gang-scheduling isn't empty, input is passed to `.spec.minMember` in PodGroup. Note that, when using this field, you need to make sure the application supports resizing (e.g., Elastic Horovod).  If not set, it defaults to the number of workers.  # noqa: E501

        :return: The min_available of this V2beta1SchedulingPolicy.  # noqa: E501
        :rtype: int
        """
        return self._min_available

    @min_available.setter
    def min_available(self, min_available):
        """Sets the min_available of this V2beta1SchedulingPolicy.

        MinAvailable defines the minimal number of member to run the PodGroup. If the gang-scheduling isn't empty, input is passed to `.spec.minMember` in PodGroup. Note that, when using this field, you need to make sure the application supports resizing (e.g., Elastic Horovod).  If not set, it defaults to the number of workers.  # noqa: E501

        :param min_available: The min_available of this V2beta1SchedulingPolicy.  # noqa: E501
        :type min_available: int
        """

        self._min_available = min_available

    @property
    def min_resources(self):
        """Gets the min_resources of this V2beta1SchedulingPolicy.  # noqa: E501

        MinResources defines the minimal resources of members to run the PodGroup. If the gang-scheduling isn't empty, input is passed to `.spec.minResources` in PodGroup for scheduler-plugins.  # noqa: E501

        :return: The min_resources of this V2beta1SchedulingPolicy.  # noqa: E501
        :rtype: dict(str, ResourceQuantity)
        """
        return self._min_resources

    @min_resources.setter
    def min_resources(self, min_resources):
        """Sets the min_resources of this V2beta1SchedulingPolicy.

        MinResources defines the minimal resources of members to run the PodGroup. If the gang-scheduling isn't empty, input is passed to `.spec.minResources` in PodGroup for scheduler-plugins.  # noqa: E501

        :param min_resources: The min_resources of this V2beta1SchedulingPolicy.  # noqa: E501
        :type min_resources: dict(str, ResourceQuantity)
        """

        self._min_resources = min_resources

    @property
    def priority_class(self):
        """Gets the priority_class of this V2beta1SchedulingPolicy.  # noqa: E501

        PriorityClass defines the PodGroup's PriorityClass. If the gang-scheduling is set to the volcano, input is passed to `.spec.priorityClassName` in PodGroup for volcano, and if it is set to the scheduler-plugins, input isn't passed to PodGroup for scheduler-plugins.  # noqa: E501

        :return: The priority_class of this V2beta1SchedulingPolicy.  # noqa: E501
        :rtype: str
        """
        return self._priority_class

    @priority_class.setter
    def priority_class(self, priority_class):
        """Sets the priority_class of this V2beta1SchedulingPolicy.

        PriorityClass defines the PodGroup's PriorityClass. If the gang-scheduling is set to the volcano, input is passed to `.spec.priorityClassName` in PodGroup for volcano, and if it is set to the scheduler-plugins, input isn't passed to PodGroup for scheduler-plugins.  # noqa: E501

        :param priority_class: The priority_class of this V2beta1SchedulingPolicy.  # noqa: E501
        :type priority_class: str
        """

        self._priority_class = priority_class

    @property
    def queue(self):
        """Gets the queue of this V2beta1SchedulingPolicy.  # noqa: E501

        Queue defines the queue name to allocate resource for PodGroup. If the gang-scheduling is set to the volcano, input is passed to `.spec.queue` in PodGroup for the volcano, and if it is set to the scheduler-plugins, input isn't passed to PodGroup.  # noqa: E501

        :return: The queue of this V2beta1SchedulingPolicy.  # noqa: E501
        :rtype: str
        """
        return self._queue

    @queue.setter
    def queue(self, queue):
        """Sets the queue of this V2beta1SchedulingPolicy.

        Queue defines the queue name to allocate resource for PodGroup. If the gang-scheduling is set to the volcano, input is passed to `.spec.queue` in PodGroup for the volcano, and if it is set to the scheduler-plugins, input isn't passed to PodGroup.  # noqa: E501

        :param queue: The queue of this V2beta1SchedulingPolicy.  # noqa: E501
        :type queue: str
        """

        self._queue = queue

    @property
    def schedule_timeout_seconds(self):
        """Gets the schedule_timeout_seconds of this V2beta1SchedulingPolicy.  # noqa: E501

        SchedulerTimeoutSeconds defines the maximal time of members to wait before run the PodGroup. If the gang-scheduling is set to the scheduler-plugins, input is passed to `.spec.scheduleTimeoutSeconds` in PodGroup for the scheduler-plugins, and if it is set to the volcano, input isn't passed to PodGroup.  # noqa: E501

        :return: The schedule_timeout_seconds of this V2beta1SchedulingPolicy.  # noqa: E501
        :rtype: int
        """
        return self._schedule_timeout_seconds

    @schedule_timeout_seconds.setter
    def schedule_timeout_seconds(self, schedule_timeout_seconds):
        """Sets the schedule_timeout_seconds of this V2beta1SchedulingPolicy.

        SchedulerTimeoutSeconds defines the maximal time of members to wait before run the PodGroup. If the gang-scheduling is set to the scheduler-plugins, input is passed to `.spec.scheduleTimeoutSeconds` in PodGroup for the scheduler-plugins, and if it is set to the volcano, input isn't passed to PodGroup.  # noqa: E501

        :param schedule_timeout_seconds: The schedule_timeout_seconds of this V2beta1SchedulingPolicy.  # noqa: E501
        :type schedule_timeout_seconds: int
        """

        self._schedule_timeout_seconds = schedule_timeout_seconds

    def to_dict(self, serialize=False):
        """Returns the model properties as a dict"""
        result = {}

        def convert(x):
            if hasattr(x, "to_dict"):
                args = inspect.getargspec(x.to_dict).args
                if len(args) == 1:
                    return x.to_dict()
                else:
                    return x.to_dict(serialize)
            else:
                return x

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            attr = self.attribute_map.get(attr, attr) if serialize else attr
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: convert(x),
                    value
                ))
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], convert(item[1])),
                    value.items()
                ))
            else:
                result[attr] = convert(value)

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V2beta1SchedulingPolicy):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V2beta1SchedulingPolicy):
            return True

        return self.to_dict() != other.to_dict()
