# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


import inspect
import pprint
import re  # noqa: F401
import six

from mpijob.configuration import Configuration


class K8sIoApimachineryPkgVersionInfo(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'build_date': 'str',
        'compiler': 'str',
        'git_commit': 'str',
        'git_tree_state': 'str',
        'git_version': 'str',
        'go_version': 'str',
        'major': 'str',
        'minor': 'str',
        'platform': 'str'
    }

    attribute_map = {
        'build_date': 'buildDate',
        'compiler': 'compiler',
        'git_commit': 'gitCommit',
        'git_tree_state': 'gitTreeState',
        'git_version': 'gitVersion',
        'go_version': 'goVersion',
        'major': 'major',
        'minor': 'minor',
        'platform': 'platform'
    }

    def __init__(self, build_date='', compiler='', git_commit='', git_tree_state='', git_version='', go_version='', major='', minor='', platform='', local_vars_configuration=None):  # noqa: E501
        """K8sIoApimachineryPkgVersionInfo - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration.get_default_copy()
        self.local_vars_configuration = local_vars_configuration

        self._build_date = None
        self._compiler = None
        self._git_commit = None
        self._git_tree_state = None
        self._git_version = None
        self._go_version = None
        self._major = None
        self._minor = None
        self._platform = None
        self.discriminator = None

        self.build_date = build_date
        self.compiler = compiler
        self.git_commit = git_commit
        self.git_tree_state = git_tree_state
        self.git_version = git_version
        self.go_version = go_version
        self.major = major
        self.minor = minor
        self.platform = platform

    @property
    def build_date(self):
        """Gets the build_date of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501


        :return: The build_date of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :rtype: str
        """
        return self._build_date

    @build_date.setter
    def build_date(self, build_date):
        """Sets the build_date of this K8sIoApimachineryPkgVersionInfo.


        :param build_date: The build_date of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :type build_date: str
        """
        if self.local_vars_configuration.client_side_validation and build_date is None:  # noqa: E501
            raise ValueError("Invalid value for `build_date`, must not be `None`")  # noqa: E501

        self._build_date = build_date

    @property
    def compiler(self):
        """Gets the compiler of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501


        :return: The compiler of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :rtype: str
        """
        return self._compiler

    @compiler.setter
    def compiler(self, compiler):
        """Sets the compiler of this K8sIoApimachineryPkgVersionInfo.


        :param compiler: The compiler of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :type compiler: str
        """
        if self.local_vars_configuration.client_side_validation and compiler is None:  # noqa: E501
            raise ValueError("Invalid value for `compiler`, must not be `None`")  # noqa: E501

        self._compiler = compiler

    @property
    def git_commit(self):
        """Gets the git_commit of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501


        :return: The git_commit of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :rtype: str
        """
        return self._git_commit

    @git_commit.setter
    def git_commit(self, git_commit):
        """Sets the git_commit of this K8sIoApimachineryPkgVersionInfo.


        :param git_commit: The git_commit of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :type git_commit: str
        """
        if self.local_vars_configuration.client_side_validation and git_commit is None:  # noqa: E501
            raise ValueError("Invalid value for `git_commit`, must not be `None`")  # noqa: E501

        self._git_commit = git_commit

    @property
    def git_tree_state(self):
        """Gets the git_tree_state of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501


        :return: The git_tree_state of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :rtype: str
        """
        return self._git_tree_state

    @git_tree_state.setter
    def git_tree_state(self, git_tree_state):
        """Sets the git_tree_state of this K8sIoApimachineryPkgVersionInfo.


        :param git_tree_state: The git_tree_state of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :type git_tree_state: str
        """
        if self.local_vars_configuration.client_side_validation and git_tree_state is None:  # noqa: E501
            raise ValueError("Invalid value for `git_tree_state`, must not be `None`")  # noqa: E501

        self._git_tree_state = git_tree_state

    @property
    def git_version(self):
        """Gets the git_version of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501


        :return: The git_version of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :rtype: str
        """
        return self._git_version

    @git_version.setter
    def git_version(self, git_version):
        """Sets the git_version of this K8sIoApimachineryPkgVersionInfo.


        :param git_version: The git_version of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :type git_version: str
        """
        if self.local_vars_configuration.client_side_validation and git_version is None:  # noqa: E501
            raise ValueError("Invalid value for `git_version`, must not be `None`")  # noqa: E501

        self._git_version = git_version

    @property
    def go_version(self):
        """Gets the go_version of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501


        :return: The go_version of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :rtype: str
        """
        return self._go_version

    @go_version.setter
    def go_version(self, go_version):
        """Sets the go_version of this K8sIoApimachineryPkgVersionInfo.


        :param go_version: The go_version of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :type go_version: str
        """
        if self.local_vars_configuration.client_side_validation and go_version is None:  # noqa: E501
            raise ValueError("Invalid value for `go_version`, must not be `None`")  # noqa: E501

        self._go_version = go_version

    @property
    def major(self):
        """Gets the major of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501


        :return: The major of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :rtype: str
        """
        return self._major

    @major.setter
    def major(self, major):
        """Sets the major of this K8sIoApimachineryPkgVersionInfo.


        :param major: The major of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :type major: str
        """
        if self.local_vars_configuration.client_side_validation and major is None:  # noqa: E501
            raise ValueError("Invalid value for `major`, must not be `None`")  # noqa: E501

        self._major = major

    @property
    def minor(self):
        """Gets the minor of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501


        :return: The minor of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :rtype: str
        """
        return self._minor

    @minor.setter
    def minor(self, minor):
        """Sets the minor of this K8sIoApimachineryPkgVersionInfo.


        :param minor: The minor of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :type minor: str
        """
        if self.local_vars_configuration.client_side_validation and minor is None:  # noqa: E501
            raise ValueError("Invalid value for `minor`, must not be `None`")  # noqa: E501

        self._minor = minor

    @property
    def platform(self):
        """Gets the platform of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501


        :return: The platform of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :rtype: str
        """
        return self._platform

    @platform.setter
    def platform(self, platform):
        """Sets the platform of this K8sIoApimachineryPkgVersionInfo.


        :param platform: The platform of this K8sIoApimachineryPkgVersionInfo.  # noqa: E501
        :type platform: str
        """
        if self.local_vars_configuration.client_side_validation and platform is None:  # noqa: E501
            raise ValueError("Invalid value for `platform`, must not be `None`")  # noqa: E501

        self._platform = platform

    def to_dict(self, serialize=False):
        """Returns the model properties as a dict"""
        result = {}

        def convert(x):
            if hasattr(x, "to_dict"):
                args = inspect.getargspec(x.to_dict).args
                if len(args) == 1:
                    return x.to_dict()
                else:
                    return x.to_dict(serialize)
            else:
                return x

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            attr = self.attribute_map.get(attr, attr) if serialize else attr
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: convert(x),
                    value
                ))
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], convert(item[1])),
                    value.items()
                ))
            else:
                result[attr] = convert(value)

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, K8sIoApimachineryPkgVersionInfo):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, K8sIoApimachineryPkgVersionInfo):
            return True

        return self.to_dict() != other.to_dict()
