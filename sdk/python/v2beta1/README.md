# kubeflow
Python SDK for MPI-Operator

This Python package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

- API version: v2beta1
- Package version: 0.1
- Build package: org.openapitools.codegen.languages.PythonLegacyClientCodegen

## Requirements.

Python 2.7 and 3.4+

## Installation & Usage
### pip install

If the python package is hosted on a repository, you can install directly using:

```sh
pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git
```
(you may need to run `pip` with root permission: `sudo pip install git+https://github.com/GIT_USER_ID/GIT_REPO_ID.git`)

Then import the package:
```python
import mpijob
```

### Setuptools

Install via [Setuptools](http://pypi.python.org/pypi/setuptools).

```sh
python setup.py install --user
```
(or `sudo python setup.py install` to install the package for all users)

Then import the package:
```python
import mpijob
```

## Getting Started

Please follow the [installation procedure](#installation--usage) and then run the following:

```python
from __future__ import print_function

import time
import mpijob
from mpijob.rest import ApiException
from pprint import pprint

```

## Documentation for API Endpoints

All URIs are relative to *http://localhost*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------


## Documentation For Models

 - [K8sIoApimachineryPkgRuntimeTypeMeta](docs/K8sIoApimachineryPkgRuntimeTypeMeta.md)
 - [K8sIoApimachineryPkgRuntimeUnknown](docs/K8sIoApimachineryPkgRuntimeUnknown.md)
 - [K8sIoApimachineryPkgVersionInfo](docs/K8sIoApimachineryPkgVersionInfo.md)
 - [V1APIGroup](docs/V1APIGroup.md)
 - [V1APIGroupList](docs/V1APIGroupList.md)
 - [V1APIResource](docs/V1APIResource.md)
 - [V1APIResourceList](docs/V1APIResourceList.md)
 - [V1APIVersions](docs/V1APIVersions.md)
 - [V1ApplyOptions](docs/V1ApplyOptions.md)
 - [V1Condition](docs/V1Condition.md)
 - [V1CreateOptions](docs/V1CreateOptions.md)
 - [V1DeleteOptions](docs/V1DeleteOptions.md)
 - [V1FieldSelectorRequirement](docs/V1FieldSelectorRequirement.md)
 - [V1GetOptions](docs/V1GetOptions.md)
 - [V1GroupKind](docs/V1GroupKind.md)
 - [V1GroupResource](docs/V1GroupResource.md)
 - [V1GroupVersion](docs/V1GroupVersion.md)
 - [V1GroupVersionForDiscovery](docs/V1GroupVersionForDiscovery.md)
 - [V1GroupVersionKind](docs/V1GroupVersionKind.md)
 - [V1GroupVersionResource](docs/V1GroupVersionResource.md)
 - [V1InternalEvent](docs/V1InternalEvent.md)
 - [V1LabelSelector](docs/V1LabelSelector.md)
 - [V1LabelSelectorRequirement](docs/V1LabelSelectorRequirement.md)
 - [V1List](docs/V1List.md)
 - [V1ListMeta](docs/V1ListMeta.md)
 - [V1ListOptions](docs/V1ListOptions.md)
 - [V1ManagedFieldsEntry](docs/V1ManagedFieldsEntry.md)
 - [V1ObjectMeta](docs/V1ObjectMeta.md)
 - [V1OwnerReference](docs/V1OwnerReference.md)
 - [V1PartialObjectMetadata](docs/V1PartialObjectMetadata.md)
 - [V1PartialObjectMetadataList](docs/V1PartialObjectMetadataList.md)
 - [V1PatchOptions](docs/V1PatchOptions.md)
 - [V1Preconditions](docs/V1Preconditions.md)
 - [V1RootPaths](docs/V1RootPaths.md)
 - [V1ServerAddressByClientCIDR](docs/V1ServerAddressByClientCIDR.md)
 - [V1Status](docs/V1Status.md)
 - [V1StatusCause](docs/V1StatusCause.md)
 - [V1StatusDetails](docs/V1StatusDetails.md)
 - [V1Table](docs/V1Table.md)
 - [V1TableColumnDefinition](docs/V1TableColumnDefinition.md)
 - [V1TableOptions](docs/V1TableOptions.md)
 - [V1TableRow](docs/V1TableRow.md)
 - [V1TableRowCondition](docs/V1TableRowCondition.md)
 - [V1Timestamp](docs/V1Timestamp.md)
 - [V1TypeMeta](docs/V1TypeMeta.md)
 - [V1UpdateOptions](docs/V1UpdateOptions.md)
 - [V1WatchEvent](docs/V1WatchEvent.md)
 - [V2beta1JobCondition](docs/V2beta1JobCondition.md)
 - [V2beta1JobStatus](docs/V2beta1JobStatus.md)
 - [V2beta1MPIJob](docs/V2beta1MPIJob.md)
 - [V2beta1MPIJobList](docs/V2beta1MPIJobList.md)
 - [V2beta1MPIJobSpec](docs/V2beta1MPIJobSpec.md)
 - [V2beta1ReplicaSpec](docs/V2beta1ReplicaSpec.md)
 - [V2beta1ReplicaStatus](docs/V2beta1ReplicaStatus.md)
 - [V2beta1RunPolicy](docs/V2beta1RunPolicy.md)
 - [V2beta1SchedulingPolicy](docs/V2beta1SchedulingPolicy.md)


## Documentation For Authorization

 All endpoints do not require authorization.

## Author



