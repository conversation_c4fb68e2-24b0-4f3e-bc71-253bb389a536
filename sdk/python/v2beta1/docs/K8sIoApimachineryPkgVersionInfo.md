# K8sIoApimachineryPkgVersionInfo

Info contains versioning information. how we'll want to distribute that information.

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**build_date** | **str** |  | [default to '']
**compiler** | **str** |  | [default to '']
**git_commit** | **str** |  | [default to '']
**git_tree_state** | **str** |  | [default to '']
**git_version** | **str** |  | [default to '']
**go_version** | **str** |  | [default to '']
**major** | **str** |  | [default to '']
**minor** | **str** |  | [default to '']
**platform** | **str** |  | [default to '']

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


