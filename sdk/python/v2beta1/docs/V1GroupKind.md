# V1GroupKind

GroupKind specifies a Group and a Kind, but does not force a version.  This is useful for identifying concepts during lookup stages without having partially valid types

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**group** | **str** |  | [default to '']
**kind** | **str** |  | [default to '']

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


