# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


from __future__ import absolute_import

import unittest
import datetime

import mpijob
from mpijob.models.v1_api_group import V1APIGroup  # noqa: E501
from mpijob.rest import ApiException

class TestV1APIGroup(unittest.TestCase):
    """V1APIGroup unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional):
        """Test V1APIGroup
            include_option is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # model = mpijob.models.v1_api_group.V1APIGroup()  # noqa: E501
        if include_optional :
            return V1APIGroup(
                api_version = '', 
                kind = '', 
                name = '', 
                preferred_version = mpijob.models.v1/group_version_for_discovery.v1.GroupVersionForDiscovery(
                    group_version = '', 
                    version = '', ), 
                server_address_by_client_cidrs = [
                    mpijob.models.v1/server_address_by_client_cidr.v1.ServerAddressByClientCIDR(
                        client_cidr = '', 
                        server_address = '', )
                    ], 
                versions = [
                    mpijob.models.v1/group_version_for_discovery.v1.GroupVersionForDiscovery(
                        group_version = '', 
                        version = '', )
                    ]
            )
        else :
            return V1APIGroup(
                name = '',
                versions = [
                    mpijob.models.v1/group_version_for_discovery.v1.GroupVersionForDiscovery(
                        group_version = '', 
                        version = '', )
                    ],
        )

    def testV1APIGroup(self):
        """Test V1APIGroup"""
        inst_req_only = self.make_instance(include_optional=False)
        inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
