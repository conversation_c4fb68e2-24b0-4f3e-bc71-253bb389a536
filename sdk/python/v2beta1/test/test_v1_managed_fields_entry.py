# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


from __future__ import absolute_import

import unittest
import datetime

import mpijob
from mpijob.models.v1_managed_fields_entry import V1ManagedFieldsEntry  # noqa: E501
from mpijob.rest import ApiException

class TestV1ManagedFieldsEntry(unittest.TestCase):
    """V1ManagedFieldsEntry unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional):
        """Test V1ManagedFieldsEntry
            include_option is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # model = mpijob.models.v1_managed_fields_entry.V1ManagedFieldsEntry()  # noqa: E501
        if include_optional :
            return V1ManagedFieldsEntry(
                api_version = '', 
                fields_type = '', 
                fields_v1 = None, 
                manager = '', 
                operation = '', 
                subresource = '', 
                time = datetime.datetime.strptime('2013-10-20 19:20:30.00', '%Y-%m-%d %H:%M:%S.%f')
            )
        else :
            return V1ManagedFieldsEntry(
        )

    def testV1ManagedFieldsEntry(self):
        """Test V1ManagedFieldsEntry"""
        inst_req_only = self.make_instance(include_optional=False)
        inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
