# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


from __future__ import absolute_import

import unittest
import datetime

import mpijob
from mpijob.models.v1_api_resource_list import V1APIResourceList  # noqa: E501
from mpijob.rest import ApiException

class TestV1APIResourceList(unittest.TestCase):
    """V1APIResourceList unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional):
        """Test V1APIResourceList
            include_option is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # model = mpijob.models.v1_api_resource_list.V1APIResourceList()  # noqa: E501
        if include_optional :
            return V1APIResourceList(
                api_version = '', 
                group_version = '', 
                kind = '', 
                resources = [
                    mpijob.models.v1/api_resource.v1.APIResource(
                        categories = [
                            ''
                            ], 
                        group = '', 
                        kind = '', 
                        name = '', 
                        namespaced = True, 
                        short_names = [
                            ''
                            ], 
                        singular_name = '', 
                        storage_version_hash = '', 
                        verbs = [
                            ''
                            ], 
                        version = '', )
                    ]
            )
        else :
            return V1APIResourceList(
                group_version = '',
                resources = [
                    mpijob.models.v1/api_resource.v1.APIResource(
                        categories = [
                            ''
                            ], 
                        group = '', 
                        kind = '', 
                        name = '', 
                        namespaced = True, 
                        short_names = [
                            ''
                            ], 
                        singular_name = '', 
                        storage_version_hash = '', 
                        verbs = [
                            ''
                            ], 
                        version = '', )
                    ],
        )

    def testV1APIResourceList(self):
        """Test V1APIResourceList"""
        inst_req_only = self.make_instance(include_optional=False)
        inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
