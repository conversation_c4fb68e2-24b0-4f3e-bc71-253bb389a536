# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


from __future__ import absolute_import

import unittest
import datetime

import mpijob
from mpijob.models.k8s_io_apimachinery_pkg_runtime_unknown import K8sIoApimachineryPkgRuntimeUnknown  # noqa: E501
from mpijob.rest import ApiException

class TestK8sIoApimachineryPkgRuntimeUnknown(unittest.TestCase):
    """K8sIoApimachineryPkgRuntimeUnknown unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional):
        """Test K8sIoApimachineryPkgRuntimeUnknown
            include_option is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # model = mpijob.models.k8s_io_apimachinery_pkg_runtime_unknown.K8sIoApimachineryPkgRuntimeUnknown()  # noqa: E501
        if include_optional :
            return K8sIoApimachineryPkgRuntimeUnknown(
                content_encoding = '', 
                content_type = '', 
                api_version = '', 
                kind = ''
            )
        else :
            return K8sIoApimachineryPkgRuntimeUnknown(
                content_encoding = '',
                content_type = '',
        )

    def testK8sIoApimachineryPkgRuntimeUnknown(self):
        """Test K8sIoApimachineryPkgRuntimeUnknown"""
        inst_req_only = self.make_instance(include_optional=False)
        inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
