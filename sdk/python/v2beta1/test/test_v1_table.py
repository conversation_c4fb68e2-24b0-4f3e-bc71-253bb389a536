# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


from __future__ import absolute_import

import unittest
import datetime

import mpijob
from mpijob.models.v1_table import V1Table  # noqa: E501
from mpijob.rest import ApiException

class TestV1Table(unittest.TestCase):
    """V1Table unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional):
        """Test V1Table
            include_option is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # model = mpijob.models.v1_table.V1Table()  # noqa: E501
        if include_optional :
            return V1Table(
                api_version = '', 
                column_definitions = [
                    mpijob.models.v1/table_column_definition.v1.TableColumnDefinition(
                        description = '', 
                        format = '', 
                        name = '', 
                        priority = 56, 
                        type = '', )
                    ], 
                kind = '', 
                metadata = mpijob.models.v1/list_meta.v1.ListMeta(
                    continue = '', 
                    remaining_item_count = 56, 
                    resource_version = '', 
                    self_link = '', ), 
                rows = [
                    mpijob.models.v1/table_row.v1.TableRow(
                        cells = [
                            None
                            ], 
                        conditions = [
                            mpijob.models.v1/table_row_condition.v1.TableRowCondition(
                                message = '', 
                                reason = '', 
                                status = '', 
                                type = '', )
                            ], 
                        object = mpijob.models.k8s/io/apimachinery/pkg/runtime/raw_extension.k8s.io.apimachinery.pkg.runtime.RawExtension(), )
                    ]
            )
        else :
            return V1Table(
                column_definitions = [
                    mpijob.models.v1/table_column_definition.v1.TableColumnDefinition(
                        description = '', 
                        format = '', 
                        name = '', 
                        priority = 56, 
                        type = '', )
                    ],
                rows = [
                    mpijob.models.v1/table_row.v1.TableRow(
                        cells = [
                            None
                            ], 
                        conditions = [
                            mpijob.models.v1/table_row_condition.v1.TableRowCondition(
                                message = '', 
                                reason = '', 
                                status = '', 
                                type = '', )
                            ], 
                        object = mpijob.models.k8s/io/apimachinery/pkg/runtime/raw_extension.k8s.io.apimachinery.pkg.runtime.RawExtension(), )
                    ],
        )

    def testV1Table(self):
        """Test V1Table"""
        inst_req_only = self.make_instance(include_optional=False)
        inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
