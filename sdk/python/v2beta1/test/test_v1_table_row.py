# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


from __future__ import absolute_import

import unittest
import datetime

import mpijob
from mpijob.models.v1_table_row import V1TableRow  # noqa: E501
from mpijob.rest import ApiException

class TestV1TableRow(unittest.TestCase):
    """V1TableRow unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional):
        """Test V1TableRow
            include_option is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # model = mpijob.models.v1_table_row.V1TableRow()  # noqa: E501
        if include_optional :
            return V1TableRow(
                cells = [
                    None
                    ], 
                conditions = [
                    mpijob.models.v1/table_row_condition.v1.TableRowCondition(
                        message = '', 
                        reason = '', 
                        status = '', 
                        type = '', )
                    ], 
                object = mpijob.models.k8s/io/apimachinery/pkg/runtime/raw_extension.k8s.io.apimachinery.pkg.runtime.RawExtension()
            )
        else :
            return V1TableRow(
                cells = [
                    None
                    ],
        )

    def testV1TableRow(self):
        """Test V1TableRow"""
        inst_req_only = self.make_instance(include_optional=False)
        inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
