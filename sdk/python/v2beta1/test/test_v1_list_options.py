# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


from __future__ import absolute_import

import unittest
import datetime

import mpijob
from mpijob.models.v1_list_options import V1ListOptions  # noqa: E501
from mpijob.rest import ApiException

class TestV1ListOptions(unittest.TestCase):
    """V1ListOptions unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional):
        """Test V1ListOptions
            include_option is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # model = mpijob.models.v1_list_options.V1ListOptions()  # noqa: E501
        if include_optional :
            return V1ListOptions(
                allow_watch_bookmarks = True, 
                api_version = '', 
                _continue = '', 
                field_selector = '', 
                kind = '', 
                label_selector = '', 
                limit = 56, 
                resource_version = '', 
                resource_version_match = '', 
                send_initial_events = True, 
                timeout_seconds = 56, 
                watch = True
            )
        else :
            return V1ListOptions(
        )

    def testV1ListOptions(self):
        """Test V1ListOptions"""
        inst_req_only = self.make_instance(include_optional=False)
        inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
