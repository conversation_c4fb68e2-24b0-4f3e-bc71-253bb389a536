# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


from __future__ import absolute_import

import unittest
import datetime

import mpijob
from mpijob.models.v1_job_status import V1JobStatus  # noqa: E501
from mpijob.rest import ApiException

class TestV1JobStatus(unittest.TestCase):
    """V1JobStatus unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional):
        """Test V1JobStatus
            include_option is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # model = mpijob.models.v1_job_status.V1JobStatus()  # noqa: E501
        if include_optional :
            return V1JobStatus(
                completion_time = None, 
                conditions = [
                    mpijob.models.v1/job_condition.v1.JobCondition(
                        last_transition_time = None, 
                        last_update_time = None, 
                        message = '', 
                        reason = '', 
                        status = '', 
                        type = '', )
                    ], 
                last_reconcile_time = None, 
                replica_statuses = {
                    'key' : mpijob.models.v1/replica_status.v1.ReplicaStatus(
                        active = 56, 
                        failed = 56, 
                        label_selector = None, 
                        selector = '', 
                        succeeded = 56, )
                    }, 
                start_time = None
            )
        else :
            return V1JobStatus(
                conditions = [
                    mpijob.models.v1/job_condition.v1.JobCondition(
                        last_transition_time = None, 
                        last_update_time = None, 
                        message = '', 
                        reason = '', 
                        status = '', 
                        type = '', )
                    ],
                replica_statuses = {
                    'key' : mpijob.models.v1/replica_status.v1.ReplicaStatus(
                        active = 56, 
                        failed = 56, 
                        label_selector = None, 
                        selector = '', 
                        succeeded = 56, )
                    },
        )

    def testV1JobStatus(self):
        """Test V1JobStatus"""
        inst_req_only = self.make_instance(include_optional=False)
        inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
