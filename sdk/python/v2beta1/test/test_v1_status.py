# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


from __future__ import absolute_import

import unittest
import datetime

import mpijob
from mpijob.models.v1_status import V1Status  # noqa: E501
from mpijob.rest import ApiException

class TestV1Status(unittest.TestCase):
    """V1Status unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional):
        """Test V1Status
            include_option is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # model = mpijob.models.v1_status.V1Status()  # noqa: E501
        if include_optional :
            return V1Status(
                api_version = '', 
                code = 56, 
                details = mpijob.models.v1/status_details.v1.StatusDetails(
                    causes = [
                        mpijob.models.v1/status_cause.v1.StatusCause(
                            field = '', 
                            message = '', 
                            reason = '', )
                        ], 
                    group = '', 
                    kind = '', 
                    name = '', 
                    retry_after_seconds = 56, 
                    uid = '', ), 
                kind = '', 
                message = '', 
                metadata = mpijob.models.v1/list_meta.v1.ListMeta(
                    continue = '', 
                    remaining_item_count = 56, 
                    resource_version = '', 
                    self_link = '', ), 
                reason = '', 
                status = ''
            )
        else :
            return V1Status(
        )

    def testV1Status(self):
        """Test V1Status"""
        inst_req_only = self.make_instance(include_optional=False)
        inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
