# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""


from __future__ import absolute_import

import unittest
import datetime

import mpijob
from mpijob.models.v2beta1_replica_status import V2beta1ReplicaStatus  # noqa: E501
from mpijob.rest import ApiException

class TestV2beta1ReplicaStatus(unittest.TestCase):
    """V2beta1ReplicaStatus unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional):
        """Test V2beta1ReplicaStatus
            include_option is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # model = mpijob.models.v2beta1_replica_status.V2beta1ReplicaStatus()  # noqa: E501
        if include_optional :
            return V2beta1ReplicaStatus(
                active = 56, 
                failed = 56, 
                label_selector = None, 
                selector = '', 
                succeeded = 56
            )
        else :
            return V2beta1ReplicaStatus(
        )

    def testV2beta1ReplicaStatus(self):
        """Test V2beta1ReplicaStatus"""
        inst_req_only = self.make_instance(include_optional=False)
        inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
