# coding: utf-8

"""
    mpijob

    Python SDK for MPI-Operator  # noqa: E501

    The version of the OpenAPI document: v2beta1
    Generated by: https://openapi-generator.tech
"""

import os
import setuptools

# Make sure everything is relative to setup.py
install_path = os.path.dirname(os.path.abspath(__file__))
os.chdir(install_path)

# Try to read description, otherwise fallback to short description
DESCRIPTION = "Python SDK for the MPI Operator"
try:
    with open(os.path.join(install_path, "README.md")) as fd:
        LONG_DESCRIPTION = fd.read()
except Exception:
    LONG_DESCRIPTION = DESCRIPTION

def setup():
    """
    Main entrypoint to run setup.
    """
    setuptools.setup(
        name="kubeflow-mpi",
        version="0.4.0",
        author="Kubeflow Authors",
        author_email="<EMAIL>",
        packages=setuptools.find_packages(),
        include_package_data=True,
        zip_safe=False,
        url="https://github.com/kubeflow/mpi-operator/tree/master/sdk/python/v2beta1",
        license="Apache 2.0",
        description=DESCRIPTION,
        long_description=LONG_DESCRIPTION,
        long_description_content_type="text/markdown",
        keywords="kubernetes,mpi",
        classifiers=[
            "Intended Audience :: Developers",
            "Intended Audience :: Education",
            "Intended Audience :: Science/Research",
            "License :: OSI Approved :: Apache Software License",
            "Programming Language :: Python",
            "Programming Language :: Python :: 3",
            "Programming Language :: Python :: 3 :: Only",
            "Programming Language :: Python :: 3.7",
            "Programming Language :: Python :: 3.8",
            "Programming Language :: Python :: 3.9",
            "Programming Language :: Python :: 3.10",
            "Topic :: Scientific/Engineering",
            "Topic :: Scientific/Engineering :: Artificial Intelligence",
            "Topic :: Software Development",
            "Topic :: Software Development :: Libraries",
            "Topic :: Software Development :: Libraries :: Python Modules",
            "Operating System :: OS Independent",
            "Programming Language :: Python :: 3.8",
        ],
    )


if __name__ == "__main__":
    setup()
