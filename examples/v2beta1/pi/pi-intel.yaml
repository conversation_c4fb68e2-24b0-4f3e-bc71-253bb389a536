apiVersion: kubeflow.org/v2beta1
kind: MPIJob
metadata:
  name: pi
spec:
  slotsPerWorker: 1
  runPolicy:
    cleanPodPolicy: Running
  sshAuthMountPath: /home/<USER>/.ssh
  mpiImplementation: Intel
  mpiReplicaSpecs:
    Launcher:
      replicas: 1
      template:
        spec:
          containers:
          - image: mpioperator/mpi-pi:intel
            imagePullPolicy: Always
            name: mpi-launcher
            securityContext:
              runAsUser: 1000
            args:
            - mpirun
            - -n
            - "2"
            - /home/<USER>/pi
            resources:
              limits:
                cpu: 1
                memory: 1Gi
    Worker:
      replicas: 2
      template:
        spec:
          containers:
          - image: mpioperator/mpi-pi:intel
            imagePullPolicy: Always
            name: mpi-worker
            securityContext:
              runAsUser: 1000
            command:
            args:
            - /usr/sbin/sshd
            - -De
            - -f
            - /home/<USER>/.sshd_config
            readinessProbe:
              tcpSocket:
                port: 2222
              initialDelaySeconds: 2
            resources:
              limits:
                cpu: 1
                memory: 1Gi
